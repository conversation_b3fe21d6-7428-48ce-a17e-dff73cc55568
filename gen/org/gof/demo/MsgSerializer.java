package org.gof.demo;
import org.gof.core.gen.GofGenFile;
import com.google.protobuf.CodedInputStream;
import org.gof.core.support.SysException;
import org.gof.core.InputStream;

@GofGenFile
public final class MsgSerializer{
	public static com.google.protobuf.GeneratedMessageV3 create(int id, CodedInputStream s){
		try{
			switch(id){
			case 1821824568:
				return org.gof.demo.worldsrv.msg.Define.DPayGift.parseFrom(s);
			case -1396520545:
				return org.gof.demo.worldsrv.msg.Define.p_act.parseFrom(s);
			case -1898027321:
				return org.gof.demo.worldsrv.msg.Define.p_act_adventure_goods.parseFrom(s);
			case -1086736232:
				return org.gof.demo.worldsrv.msg.Define.p_act_adventure_item_stable.parseFrom(s);
			case 2144265881:
				return org.gof.demo.worldsrv.msg.Define.p_act_adventure_space_move.parseFrom(s);
			case 848351726:
				return org.gof.demo.worldsrv.msg.Define.p_act_big_reward.parseFrom(s);
			case -1472894702:
				return org.gof.demo.worldsrv.msg.Define.p_act_broadcast_report.parseFrom(s);
			case 238314370:
				return org.gof.demo.worldsrv.msg.Define.p_act_custom_mall.parseFrom(s);
			case 687069990:
				return org.gof.demo.worldsrv.msg.Define.p_act_custom_mall_receive.parseFrom(s);
			case -2057849069:
				return org.gof.demo.worldsrv.msg.Define.p_act_custom_mall_role.parseFrom(s);
			case 1267409391:
				return org.gof.demo.worldsrv.msg.Define.p_act_draw_report.parseFrom(s);
			case 2113355816:
				return org.gof.demo.worldsrv.msg.Define.p_act_move_member.parseFrom(s);
			case -1686561701:
				return org.gof.demo.worldsrv.msg.Define.p_act_state_time.parseFrom(s);
			case 257314853:
				return org.gof.demo.worldsrv.msg.Define.p_act_task.parseFrom(s);
			case 708926827:
				return org.gof.demo.worldsrv.msg.Define.p_active_skill.parseFrom(s);
			case -1569069706:
				return org.gof.demo.worldsrv.msg.Define.p_ad.parseFrom(s);
			case 2001311182:
				return org.gof.demo.worldsrv.msg.Define.p_adventure_space_item.parseFrom(s);
			case 332877778:
				return org.gof.demo.worldsrv.msg.Define.p_airdrop_box.parseFrom(s);
			case -2026128946:
				return org.gof.demo.worldsrv.msg.Define.p_angel.parseFrom(s);
			case -373996337:
				return org.gof.demo.worldsrv.msg.Define.p_angel_array_tab_info.parseFrom(s);
			case -618652395:
				return org.gof.demo.worldsrv.msg.Define.p_angel_draw.parseFrom(s);
			case 1284658365:
				return org.gof.demo.worldsrv.msg.Define.p_angry_bird_his.parseFrom(s);
			case 1170095545:
				return org.gof.demo.worldsrv.msg.Define.p_angry_bird_unit.parseFrom(s);
			case 633449443:
				return org.gof.demo.worldsrv.msg.Define.p_arena_chat.parseFrom(s);
			case -806028247:
				return org.gof.demo.worldsrv.msg.Define.p_arena_history.parseFrom(s);
			case 633889975:
				return org.gof.demo.worldsrv.msg.Define.p_arena_rank.parseFrom(s);
			case 633903361:
				return org.gof.demo.worldsrv.msg.Define.p_arena_role.parseFrom(s);
			case -1730668299:
				return org.gof.demo.worldsrv.msg.Define.p_artifact_gem.parseFrom(s);
			case 1448947490:
				return org.gof.demo.worldsrv.msg.Define.p_artifact_gem_tab_info.parseFrom(s);
			case -1818057540:
				return org.gof.demo.worldsrv.msg.Define.p_attr_obj.parseFrom(s);
			case -2103420351:
				return org.gof.demo.worldsrv.msg.Define.p_attr_obj_list.parseFrom(s);
			case -904656895:
				return org.gof.demo.worldsrv.msg.Define.p_autumn_guess.parseFrom(s);
			case -2141712840:
				return org.gof.demo.worldsrv.msg.Define.p_autumn_pig.parseFrom(s);
			case -1293956382:
				return org.gof.demo.worldsrv.msg.Define.p_base_fighter.parseFrom(s);
			case -1823342468:
				return org.gof.demo.worldsrv.msg.Define.p_battle_attack.parseFrom(s);
			case -1755210333:
				return org.gof.demo.worldsrv.msg.Define.p_battle_damage.parseFrom(s);
			case -1473328244:
				return org.gof.demo.worldsrv.msg.Define.p_battle_gun.parseFrom(s);
			case -409820210:
				return org.gof.demo.worldsrv.msg.Define.p_battle_hp_state.parseFrom(s);
			case 1222933542:
				return org.gof.demo.worldsrv.msg.Define.p_battle_monster.parseFrom(s);
			case -924746280:
				return org.gof.demo.worldsrv.msg.Define.p_battle_operator.parseFrom(s);
			case 1571773280:
				return org.gof.demo.worldsrv.msg.Define.p_battle_rank.parseFrom(s);
			case 1571786666:
				return org.gof.demo.worldsrv.msg.Define.p_battle_role.parseFrom(s);
			case 1571809648:
				return org.gof.demo.worldsrv.msg.Define.p_battle_ship.parseFrom(s);
			case 1484254151:
				return org.gof.demo.worldsrv.msg.Define.p_battle_video.parseFrom(s);
			case 568694498:
				return org.gof.demo.worldsrv.msg.Define.p_box_tower.parseFrom(s);
			case -730659911:
				return org.gof.demo.worldsrv.msg.Define.p_break_gold_egg.parseFrom(s);
			case 1232869017:
				return org.gof.demo.worldsrv.msg.Define.p_brief_red.parseFrom(s);
			case 967957405:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_car.parseFrom(s);
			case -57744674:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_null.parseFrom(s);
			case -1076624639:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_parking.parseFrom(s);
			case -592435688:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_protect.parseFrom(s);
			case 441421800:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_record.parseFrom(s);
			case -57605420:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_skin.parseFrom(s);
			case -1785626993:
				return org.gof.demo.worldsrv.msg.Define.p_car_park_space.parseFrom(s);
			case 1247441092:
				return org.gof.demo.worldsrv.msg.Define.p_card_eliminate_buff_effect.parseFrom(s);
			case 2127093545:
				return org.gof.demo.worldsrv.msg.Define.p_card_eliminate_card.parseFrom(s);
			case 2127600030:
				return org.gof.demo.worldsrv.msg.Define.p_card_eliminate_task.parseFrom(s);
			case 1914673381:
				return org.gof.demo.worldsrv.msg.Define.p_card_msg.parseFrom(s);
			case 1965794625:
				return org.gof.demo.worldsrv.msg.Define.p_charm_collection_room.parseFrom(s);
			case 1988619131:
				return org.gof.demo.worldsrv.msg.Define.p_charm_like_info.parseFrom(s);
			case 1644052603:
				return org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.parseFrom(s);
			case -1806943781:
				return org.gof.demo.worldsrv.msg.Define.p_charm_role_area.parseFrom(s);
			case -342400021:
				return org.gof.demo.worldsrv.msg.Define.p_chat.parseFrom(s);
			case -337567904:
				return org.gof.demo.worldsrv.msg.Define.p_chat_bubble.parseFrom(s);
			case 1090231139:
				return org.gof.demo.worldsrv.msg.Define.p_chat_elem.parseFrom(s);
			case -225610382:
				return org.gof.demo.worldsrv.msg.Define.p_chat_friend.parseFrom(s);
			case -760929120:
				return org.gof.demo.worldsrv.msg.Define.p_christmas_gift.parseFrom(s);
			case 2146979755:
				return org.gof.demo.worldsrv.msg.Define.p_client_data.parseFrom(s);
			case -24708767:
				return org.gof.demo.worldsrv.msg.Define.p_collect_blessing_luck.parseFrom(s);
			case -973699855:
				return org.gof.demo.worldsrv.msg.Define.p_collection.parseFrom(s);
			case 1680429868:
				return org.gof.demo.worldsrv.msg.Define.p_collection_list.parseFrom(s);
			case -963090706:
				return org.gof.demo.worldsrv.msg.Define.p_combat_role.parseFrom(s);
			case 396328535:
				return org.gof.demo.worldsrv.msg.Define.p_common_role.parseFrom(s);
			case -598717253:
				return org.gof.demo.worldsrv.msg.Define.p_common_role2.parseFrom(s);
			case -1551648921:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park.parseFrom(s);
			case 2024213464:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park_belong_change.parseFrom(s);
			case 981799850:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park_fighting.parseFrom(s);
			case 2097015536:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park_preview.parseFrom(s);
			case -440997188:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park_queue_role.parseFrom(s);
			case 308925956:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park_rank.parseFrom(s);
			case 1843303260:
				return org.gof.demo.worldsrv.msg.Define.p_cross_car_park_top_three.parseFrom(s);
			case 961963240:
				return org.gof.demo.worldsrv.msg.Define.p_cross_war_history_report.parseFrom(s);
			case 1087124284:
				return org.gof.demo.worldsrv.msg.Define.p_cross_war_idle_reward_info.parseFrom(s);
			case -1055322041:
				return org.gof.demo.worldsrv.msg.Define.p_cross_war_kill.parseFrom(s);
			case -1055087361:
				return org.gof.demo.worldsrv.msg.Define.p_cross_war_serv.parseFrom(s);
			case 1066663102:
				return org.gof.demo.worldsrv.msg.Define.p_custom_mall_info.parseFrom(s);
			case 349330036:
				return org.gof.demo.worldsrv.msg.Define.p_dc_teammate_plan_info.parseFrom(s);
			case 2029194325:
				return org.gof.demo.worldsrv.msg.Define.p_dead_battle_role.parseFrom(s);
			case -163971388:
				return org.gof.demo.worldsrv.msg.Define.p_develop_array.parseFrom(s);
			case -685065594:
				return org.gof.demo.worldsrv.msg.Define.p_develop_effect.parseFrom(s);
			case -314711150:
				return org.gof.demo.worldsrv.msg.Define.p_double_chapter_help_player.parseFrom(s);
			case 431372751:
				return org.gof.demo.worldsrv.msg.Define.p_double_chapter_teammate.parseFrom(s);
			case 561317057:
				return org.gof.demo.worldsrv.msg.Define.p_double_chapter_teammate_detail.parseFrom(s);
			case -382980586:
				return org.gof.demo.worldsrv.msg.Define.p_draw_info.parseFrom(s);
			case 1379704135:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon.parseFrom(s);
			case -774233883:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_change_info.parseFrom(s);
			case 768797577:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_effect.parseFrom(s);
			case 1327061936:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_event_param.parseFrom(s);
			case 447798078:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_grid.parseFrom(s);
			case 1592635678:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_hp_change_info.parseFrom(s);
			case 447853734:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_info.parseFrom(s);
			case 30470777:
				return org.gof.demo.worldsrv.msg.Define.p_dungeon_scene_info.parseFrom(s);
			case -2022456813:
				return org.gof.demo.worldsrv.msg.Define.p_emoji.parseFrom(s);
			case -2022331907:
				return org.gof.demo.worldsrv.msg.Define.p_equip.parseFrom(s);
			case 1907960229:
				return org.gof.demo.worldsrv.msg.Define.p_farm_battle_report.parseFrom(s);
			case 922664231:
				return org.gof.demo.worldsrv.msg.Define.p_farm_battle_role.parseFrom(s);
			case 1332571498:
				return org.gof.demo.worldsrv.msg.Define.p_farm_building.parseFrom(s);
			case -1644699578:
				return org.gof.demo.worldsrv.msg.Define.p_farm_crop.parseFrom(s);
			case -1644447839:
				return org.gof.demo.worldsrv.msg.Define.p_farm_land.parseFrom(s);
			case -1438519602:
				return org.gof.demo.worldsrv.msg.Define.p_farm_log.parseFrom(s);
			case 417939680:
				return org.gof.demo.worldsrv.msg.Define.p_farm_robber.parseFrom(s);
			case 1132740151:
				return org.gof.demo.worldsrv.msg.Define.p_farm_search_role.parseFrom(s);
			case 70897412:
				return org.gof.demo.worldsrv.msg.Define.p_farm_self_stolen.parseFrom(s);
			case 379466486:
				return org.gof.demo.worldsrv.msg.Define.p_farm_statue_attr.parseFrom(s);
			case 1397731824:
				return org.gof.demo.worldsrv.msg.Define.p_farm_statue_tab.parseFrom(s);
			case -342316801:
				return org.gof.demo.worldsrv.msg.Define.p_fate.parseFrom(s);
			case -1388645612:
				return org.gof.demo.worldsrv.msg.Define.p_fate_pos.parseFrom(s);
			case -98338566:
				return org.gof.demo.worldsrv.msg.Define.p_fate_pray.parseFrom(s);
			case -1206487726:
				return org.gof.demo.worldsrv.msg.Define.p_favor_friend.parseFrom(s);
			case 1865740163:
				return org.gof.demo.worldsrv.msg.Define.p_flower_history.parseFrom(s);
			case -78360016:
				return org.gof.demo.worldsrv.msg.Define.p_fly_base.parseFrom(s);
			case -1213425008:
				return org.gof.demo.worldsrv.msg.Define.p_fly_base_pet.parseFrom(s);
			case -1387998010:
				return org.gof.demo.worldsrv.msg.Define.p_fly_egg.parseFrom(s);
			case 761901210:
				return org.gof.demo.worldsrv.msg.Define.p_fly_hybrid_role.parseFrom(s);
			case -1387987488:
				return org.gof.demo.worldsrv.msg.Define.p_fly_pet.parseFrom(s);
			case 134465705:
				return org.gof.demo.worldsrv.msg.Define.p_fly_shelves.parseFrom(s);
			case -1387982264:
				return org.gof.demo.worldsrv.msg.Define.p_fly_use.parseFrom(s);
			case -73557628:
				return org.gof.demo.worldsrv.msg.Define.p_forum_news.parseFrom(s);
			case -1854900832:
				return org.gof.demo.worldsrv.msg.Define.p_forum_reward.parseFrom(s);
			case -585893444:
				return org.gof.demo.worldsrv.msg.Define.p_friend_info.parseFrom(s);
			case -2021378591:
				return org.gof.demo.worldsrv.msg.Define.p_fruit.parseFrom(s);
			case 500862060:
				return org.gof.demo.worldsrv.msg.Define.p_fruit_merge_state.parseFrom(s);
			case 471672816:
				return org.gof.demo.worldsrv.msg.Define.p_game_role.parseFrom(s);
			case -1780795167:
				return org.gof.demo.worldsrv.msg.Define.p_game_role_simple.parseFrom(s);
			case 471672917:
				return org.gof.demo.worldsrv.msg.Define.p_game_room.parseFrom(s);
			case 1777438034:
				return org.gof.demo.worldsrv.msg.Define.p_game_room_recommend.parseFrom(s);
			case -1854092171:
				return org.gof.demo.worldsrv.msg.Define.p_game_room_settle.parseFrom(s);
			case -2020550365:
				return org.gof.demo.worldsrv.msg.Define.p_goods.parseFrom(s);
			case 205864895:
				return org.gof.demo.worldsrv.msg.Define.p_goods_refresh.parseFrom(s);
			case 2049718595:
				return org.gof.demo.worldsrv.msg.Define.p_group_gift.parseFrom(s);
			case -712922647:
				return org.gof.demo.worldsrv.msg.Define.p_guard_ship.parseFrom(s);
			case -2020377152:
				return org.gof.demo.worldsrv.msg.Define.p_guild.parseFrom(s);
			case 211162159:
				return org.gof.demo.worldsrv.msg.Define.p_guild_apply.parseFrom(s);
			case -363746675:
				return org.gof.demo.worldsrv.msg.Define.p_guild_area_member.parseFrom(s);
			case -76957689:
				return org.gof.demo.worldsrv.msg.Define.p_guild_boss_combat.parseFrom(s);
			case 200098733:
				return org.gof.demo.worldsrv.msg.Define.p_guild_boss_member.parseFrom(s);
			case -1009701377:
				return org.gof.demo.worldsrv.msg.Define.p_guild_boss_rank.parseFrom(s);
			case -685727136:
				return org.gof.demo.worldsrv.msg.Define.p_guild_help.parseFrom(s);
			case -1007436111:
				return org.gof.demo.worldsrv.msg.Define.p_guild_help_status.parseFrom(s);
			case 1917546565:
				return org.gof.demo.worldsrv.msg.Define.p_guild_log.parseFrom(s);
			case -1710616071:
				return org.gof.demo.worldsrv.msg.Define.p_guild_member.parseFrom(s);
			case 1705478310:
				return org.gof.demo.worldsrv.msg.Define.p_guild_question_rank.parseFrom(s);
			case 1134417186:
				return org.gof.demo.worldsrv.msg.Define.p_guild_rank_info.parseFrom(s);
			case -1886518755:
				return org.gof.demo.worldsrv.msg.Define.p_guild_show_member.parseFrom(s);
			case -862378918:
				return org.gof.demo.worldsrv.msg.Define.p_guild_treasure_box.parseFrom(s);
			case -292303920:
				return org.gof.demo.worldsrv.msg.Define.p_gve_boss_member.parseFrom(s);
			case 1468397488:
				return org.gof.demo.worldsrv.msg.Define.p_gve_mission.parseFrom(s);
			case -232663781:
				return org.gof.demo.worldsrv.msg.Define.p_gve_obj.parseFrom(s);
			case -251056588:
				return org.gof.demo.worldsrv.msg.Define.p_gve_other.parseFrom(s);
			case 1377459410:
				return org.gof.demo.worldsrv.msg.Define.p_gve_role.parseFrom(s);
			case -1031161335:
				return org.gof.demo.worldsrv.msg.Define.p_gve_scene_member.parseFrom(s);
			case -5040416:
				return org.gof.demo.worldsrv.msg.Define.p_gve_team_member.parseFrom(s);
			case 2109760065:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_battle_report.parseFrom(s);
			case 1516593561:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_guild.parseFrom(s);
			case 1434406355:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_hall.parseFrom(s);
			case -230818686:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_map.parseFrom(s);
			case 18885115:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_player.parseFrom(s);
			case 70116430:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_report.parseFrom(s);
			case 1434717370:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_road.parseFrom(s);
			case 972354835:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_road_info.parseFrom(s);
			case 1434771559:
				return org.gof.demo.worldsrv.msg.Define.p_gvg_time.parseFrom(s);
			case 2088431201:
				return org.gof.demo.worldsrv.msg.Define.p_halloween_group_buy.parseFrom(s);
			case 1771995168:
				return org.gof.demo.worldsrv.msg.Define.p_halloween_group_buy_bundle.parseFrom(s);
			case 203431828:
				return org.gof.demo.worldsrv.msg.Define.p_halloween_group_buy_role.parseFrom(s);
			case -342253965:
				return org.gof.demo.worldsrv.msg.Define.p_head.parseFrom(s);
			case -1731802975:
				return org.gof.demo.worldsrv.msg.Define.p_head_frame.parseFrom(s);
			case 296216353:
				return org.gof.demo.worldsrv.msg.Define.p_history.parseFrom(s);
			case -659532597:
				return org.gof.demo.worldsrv.msg.Define.p_home_fish_detail.parseFrom(s);
			case 1365324627:
				return org.gof.demo.worldsrv.msg.Define.p_ip_info.parseFrom(s);
			case -79204523:
				return org.gof.demo.worldsrv.msg.Define.p_jobs_wakeup.parseFrom(s);
			case -108977709:
				return org.gof.demo.worldsrv.msg.Define.p_jobs_wakeup_plan.parseFrom(s);
			case 601276249:
				return org.gof.demo.worldsrv.msg.Define.p_key_key_string_list.parseFrom(s);
			case -1294569185:
				return org.gof.demo.worldsrv.msg.Define.p_key_kv_list.parseFrom(s);
			case 1294367812:
				return org.gof.demo.worldsrv.msg.Define.p_key_string.parseFrom(s);
			case 1470696559:
				return org.gof.demo.worldsrv.msg.Define.p_key_string3.parseFrom(s);
			case 2122162910:
				return org.gof.demo.worldsrv.msg.Define.p_key_value.parseFrom(s);
			case -117907105:
				return org.gof.demo.worldsrv.msg.Define.p_key_value_list.parseFrom(s);
			case -117855412:
				return org.gof.demo.worldsrv.msg.Define.p_key_value_name.parseFrom(s);
			case -1429052270:
				return org.gof.demo.worldsrv.msg.Define.p_key_value_string.parseFrom(s);
			case -1440926817:
				return org.gof.demo.worldsrv.msg.Define.p_kf_info.parseFrom(s);
			case 1893185136:
				return org.gof.demo.worldsrv.msg.Define.p_kf_msg.parseFrom(s);
			case -36669908:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_bet_history.parseFrom(s);
			case 822463034:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_knockout_match.parseFrom(s);
			case 209971300:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_loop_match.parseFrom(s);
			case -408719955:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_loop_rank.parseFrom(s);
			case -1933996706:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_loop_result.parseFrom(s);
			case 652189814:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_promote_result.parseFrom(s);
			case 626947025:
				return org.gof.demo.worldsrv.msg.Define.p_kungfu_race_team.parseFrom(s);
			case 968827820:
				return org.gof.demo.worldsrv.msg.Define.p_lang_info.parseFrom(s);
			case 1838883848:
				return org.gof.demo.worldsrv.msg.Define.p_league_solo_box.parseFrom(s);
			case 1897147496:
				return org.gof.demo.worldsrv.msg.Define.p_league_solo_box_record.parseFrom(s);
			case -342130547:
				return org.gof.demo.worldsrv.msg.Define.p_link.parseFrom(s);
			case -980337129:
				return org.gof.demo.worldsrv.msg.Define.p_login_info.parseFrom(s);
			case 567326986:
				return org.gof.demo.worldsrv.msg.Define.p_luck_koi.parseFrom(s);
			case -1003070585:
				return org.gof.demo.worldsrv.msg.Define.p_lucky_cat_report.parseFrom(s);
			case 194594516:
				return org.gof.demo.worldsrv.msg.Define.p_machine.parseFrom(s);
			case -342108598:
				return org.gof.demo.worldsrv.msg.Define.p_mail.parseFrom(s);
			case -949474920:
				return org.gof.demo.worldsrv.msg.Define.p_marry_scene_guest.parseFrom(s);
			case -1422529176:
				return org.gof.demo.worldsrv.msg.Define.p_marry_treasure_box.parseFrom(s);
			case -1454393901:
				return org.gof.demo.worldsrv.msg.Define.p_merge_block.parseFrom(s);
			case -1442219884:
				return org.gof.demo.worldsrv.msg.Define.p_merge_order.parseFrom(s);
			case 711088116:
				return org.gof.demo.worldsrv.msg.Define.p_mine_block.parseFrom(s);
			case 161508635:
				return org.gof.demo.worldsrv.msg.Define.p_mine_cell.parseFrom(s);
			case 1020733608:
				return org.gof.demo.worldsrv.msg.Define.p_mine_reward.parseFrom(s);
			case 567057973:
				return org.gof.demo.worldsrv.msg.Define.p_mini_game_chapter.parseFrom(s);
			case -788938703:
				return org.gof.demo.worldsrv.msg.Define.p_mining_chapter.parseFrom(s);
			case 710133823:
				return org.gof.demo.worldsrv.msg.Define.p_mining_shovel.parseFrom(s);
			case 858957188:
				return org.gof.demo.worldsrv.msg.Define.p_monopoly_dice.parseFrom(s);
			case 859055395:
				return org.gof.demo.worldsrv.msg.Define.p_monopoly_grid.parseFrom(s);
			case 1221846476:
				return org.gof.demo.worldsrv.msg.Define.p_monopoly_reward.parseFrom(s);
			case 538718121:
				return org.gof.demo.worldsrv.msg.Define.p_move_info.parseFrom(s);
			case 1620133106:
				return org.gof.demo.worldsrv.msg.Define.p_newyear_dinner_dish.parseFrom(s);
			case 2007591146:
				return org.gof.demo.worldsrv.msg.Define.p_obj_elem.parseFrom(s);
			case 1316498276:
				return org.gof.demo.worldsrv.msg.Define.p_obj_update.parseFrom(s);
			case 1795523061:
				return org.gof.demo.worldsrv.msg.Define.p_other_role_info.parseFrom(s);
			case -1889068981:
				return org.gof.demo.worldsrv.msg.Define.p_paint_seq.parseFrom(s);
			case 2019461126:
				return org.gof.demo.worldsrv.msg.Define.p_passive_skill.parseFrom(s);
			case 1933588011:
				return org.gof.demo.worldsrv.msg.Define.p_path_entry.parseFrom(s);
			case 975734797:
				return org.gof.demo.worldsrv.msg.Define.p_path_history.parseFrom(s);
			case -907338795:
				return org.gof.demo.worldsrv.msg.Define.p_path_info.parseFrom(s);
			case 855040495:
				return org.gof.demo.worldsrv.msg.Define.p_pay_mall_info.parseFrom(s);
			case -1396506068:
				return org.gof.demo.worldsrv.msg.Define.p_pet.parseFrom(s);
			case 1567601611:
				return org.gof.demo.worldsrv.msg.Define.p_pet_tab_info.parseFrom(s);
			case 142090791:
				return org.gof.demo.worldsrv.msg.Define.p_pet_worker_proficiency.parseFrom(s);
			case -538096076:
				return org.gof.demo.worldsrv.msg.Define.p_plan_detail.parseFrom(s);
			case -1396505759:
				return org.gof.demo.worldsrv.msg.Define.p_pos.parseFrom(s);
			case -948902315:
				return org.gof.demo.worldsrv.msg.Define.p_preview.parseFrom(s);
			case -2106816002:
				return org.gof.demo.worldsrv.msg.Define.p_privilege.parseFrom(s);
			case -1613154127:
				return org.gof.demo.worldsrv.msg.Define.p_privilege_card.parseFrom(s);
			case -232880929:
				return org.gof.demo.worldsrv.msg.Define.p_privilege_card_effect.parseFrom(s);
			case -836053849:
				return org.gof.demo.worldsrv.msg.Define.p_pw_log_message.parseFrom(s);
			case 521024112:
				return org.gof.demo.worldsrv.msg.Define.p_questionnaire.parseFrom(s);
			case -1171608790:
				return org.gof.demo.worldsrv.msg.Define.p_rank_data.parseFrom(s);
			case -1171447762:
				return org.gof.demo.worldsrv.msg.Define.p_rank_info.parseFrom(s);
			case 1454343951:
				return org.gof.demo.worldsrv.msg.Define.p_rank_request.parseFrom(s);
			case -1396504162:
				return org.gof.demo.worldsrv.msg.Define.p_red.parseFrom(s);
			case 892048303:
				return org.gof.demo.worldsrv.msg.Define.p_red_point.parseFrom(s);
			case 1442893537:
				return org.gof.demo.worldsrv.msg.Define.p_red_point_detail.parseFrom(s);
			case 1137214071:
				return org.gof.demo.worldsrv.msg.Define.p_red_role.parseFrom(s);
			case -2010692288:
				return org.gof.demo.worldsrv.msg.Define.p_relic.parseFrom(s);
			case 1625532215:
				return org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.parseFrom(s);
			case 2093369090:
				return org.gof.demo.worldsrv.msg.Define.p_reward.parseFrom(s);
			case -1551671676:
				return org.gof.demo.worldsrv.msg.Define.p_reward_id_status_list.parseFrom(s);
			case 1566831374:
				return org.gof.demo.worldsrv.msg.Define.p_reward_status_list.parseFrom(s);
			case 797964492:
				return org.gof.demo.worldsrv.msg.Define.p_rogue_report.parseFrom(s);
			case 1827951545:
				return org.gof.demo.worldsrv.msg.Define.p_rogue_skill.parseFrom(s);
			case -1660537946:
				return org.gof.demo.worldsrv.msg.Define.p_role_change.parseFrom(s);
			case -1573541158:
				return org.gof.demo.worldsrv.msg.Define.p_role_figure.parseFrom(s);
			case -193760426:
				return org.gof.demo.worldsrv.msg.Define.p_role_head.parseFrom(s);
			case -193721820:
				return org.gof.demo.worldsrv.msg.Define.p_role_info.parseFrom(s);
			case -836803475:
				return org.gof.demo.worldsrv.msg.Define.p_role_luck_koi.parseFrom(s);
			case 1656325353:
				return org.gof.demo.worldsrv.msg.Define.p_role_pet.parseFrom(s);
			case 458597534:
				return org.gof.demo.worldsrv.msg.Define.p_role_power_info.parseFrom(s);
			case -1701260389:
				return org.gof.demo.worldsrv.msg.Define.p_role_skill.parseFrom(s);
			case 929264250:
				return org.gof.demo.worldsrv.msg.Define.p_role_skin_info.parseFrom(s);
			case 188532178:
				return org.gof.demo.worldsrv.msg.Define.p_s1_air_drop.parseFrom(s);
			case 647209549:
				return org.gof.demo.worldsrv.msg.Define.p_s1_battle_report.parseFrom(s);
			case -437305393:
				return org.gof.demo.worldsrv.msg.Define.p_s1_battle_role.parseFrom(s);
			case -1404633799:
				return org.gof.demo.worldsrv.msg.Define.p_s1_bounty.parseFrom(s);
			case -1357066428:
				return org.gof.demo.worldsrv.msg.Define.p_s1_defend.parseFrom(s);
			case -153265324:
				return org.gof.demo.worldsrv.msg.Define.p_s1_grid.parseFrom(s);
			case 2006671855:
				return org.gof.demo.worldsrv.msg.Define.p_s1_grid_event.parseFrom(s);
			case -1458854696:
				return org.gof.demo.worldsrv.msg.Define.p_s1_grid_time.parseFrom(s);
			case -478015870:
				return org.gof.demo.worldsrv.msg.Define.p_s1_mini_map.parseFrom(s);
			case -153089057:
				return org.gof.demo.worldsrv.msg.Define.p_s1_move.parseFrom(s);
			case 2073273129:
				return org.gof.demo.worldsrv.msg.Define.p_s1_obj.parseFrom(s);
			case -446936381:
				return org.gof.demo.worldsrv.msg.Define.p_s1_queue.parseFrom(s);
			case -927765962:
				return org.gof.demo.worldsrv.msg.Define.p_s1_search.parseFrom(s);
			case 2073277422:
				return org.gof.demo.worldsrv.msg.Define.p_s1_spy.parseFrom(s);
			case -902334945:
				return org.gof.demo.worldsrv.msg.Define.p_s1_target.parseFrom(s);
			case -2058756108:
				return org.gof.demo.worldsrv.msg.Define.p_scene_monster.parseFrom(s);
			case 1200251409:
				return org.gof.demo.worldsrv.msg.Define.p_scene_obj.parseFrom(s);
			case 7491607:
				return org.gof.demo.worldsrv.msg.Define.p_scene_obj_update_elem.parseFrom(s);
			case -1446809956:
				return org.gof.demo.worldsrv.msg.Define.p_scene_role.parseFrom(s);
			case 1287363825:
				return org.gof.demo.worldsrv.msg.Define.p_science.parseFrom(s);
			case 1154940460:
				return org.gof.demo.worldsrv.msg.Define.p_science_tree.parseFrom(s);
			case -1904091456:
				return org.gof.demo.worldsrv.msg.Define.p_ship_equip.parseFrom(s);
			case -784755944:
				return org.gof.demo.worldsrv.msg.Define.p_ship_power_info.parseFrom(s);
			case 631699531:
				return org.gof.demo.worldsrv.msg.Define.p_ship_room.parseFrom(s);
			case 631725293:
				return org.gof.demo.worldsrv.msg.Define.p_ship_skin.parseFrom(s);
			case -360881633:
				return org.gof.demo.worldsrv.msg.Define.p_ship_treasure.parseFrom(s);
			case 394924867:
				return org.gof.demo.worldsrv.msg.Define.p_shop_item_time.parseFrom(s);
			case -1370328744:
				return org.gof.demo.worldsrv.msg.Define.p_show_mall_friend_gift_info.parseFrom(s);
			case 620901587:
				return org.gof.demo.worldsrv.msg.Define.p_show_mall_friend_info.parseFrom(s);
			case -28506994:
				return org.gof.demo.worldsrv.msg.Define.p_simple_farm_battle_role.parseFrom(s);
			case 1325016288:
				return org.gof.demo.worldsrv.msg.Define.p_simple_guild_member.parseFrom(s);
			case 1876043120:
				return org.gof.demo.worldsrv.msg.Define.p_skill_system.parseFrom(s);
			case 402766361:
				return org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.parseFrom(s);
			case 230412912:
				return org.gof.demo.worldsrv.msg.Define.p_slime_event.parseFrom(s);
			case 243018279:
				return org.gof.demo.worldsrv.msg.Define.p_slime_skill.parseFrom(s);
			case 358448711:
				return org.gof.demo.worldsrv.msg.Define.p_spy_desc.parseFrom(s);
			case -1758708837:
				return org.gof.demo.worldsrv.msg.Define.p_spy_state.parseFrom(s);
			case 358994592:
				return org.gof.demo.worldsrv.msg.Define.p_spy_vote.parseFrom(s);
			case 1782914252:
				return org.gof.demo.worldsrv.msg.Define.p_string_string.parseFrom(s);
			case -14585705:
				return org.gof.demo.worldsrv.msg.Define.p_subscribe.parseFrom(s);
			case 599699898:
				return org.gof.demo.worldsrv.msg.Define.p_system_sp.parseFrom(s);
			case -1801010536:
				return org.gof.demo.worldsrv.msg.Define.p_talent_tab_info.parseFrom(s);
			case 788578955:
				return org.gof.demo.worldsrv.msg.Define.p_tanabata_teammate_info.parseFrom(s);
			case -341899752:
				return org.gof.demo.worldsrv.msg.Define.p_task.parseFrom(s);
			case -2008718299:
				return org.gof.demo.worldsrv.msg.Define.p_title.parseFrom(s);
			case -1884604691:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_apply.parseFrom(s);
			case 1120095880:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_apply_role.parseFrom(s);
			case 1320106276:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_his_role.parseFrom(s);
			case 1320126660:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_his_serv.parseFrom(s);
			case 1093020291:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_log.parseFrom(s);
			case -475930345:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_role.parseFrom(s);
			case -475909961:
				return org.gof.demo.worldsrv.msg.Define.p_transfer_serv.parseFrom(s);
			case 1145437215:
				return org.gof.demo.worldsrv.msg.Define.p_treasure_hunt_gift.parseFrom(s);
			case 1565059171:
				return org.gof.demo.worldsrv.msg.Define.p_treasure_hunt_report.parseFrom(s);
			case 1459493965:
				return org.gof.demo.worldsrv.msg.Define.p_unlock_path.parseFrom(s);
			case 1459628998:
				return org.gof.demo.worldsrv.msg.Define.p_unlock_tree.parseFrom(s);
			case 733750933:
				return org.gof.demo.worldsrv.msg.Define.p_war_token_task.parseFrom(s);
			case 1468985858:
				return org.gof.demo.worldsrv.msg.Define.p_wing_feather.parseFrom(s);
			case -525017171:
				return org.gof.demo.worldsrv.msg.Define.p_wing_talent.parseFrom(s);
			case -992026881:
				return org.gof.demo.worldsrv.msg.Define.p_worker_base.parseFrom(s);
			case -891050348:
				return org.gof.demo.worldsrv.msg.Define.p_worker_common_info.parseFrom(s);
			case -368610956:
				return org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.parseFrom(s);
			case 1277037481:
				return org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.parseFrom(s);
			case 2110324024:
				return org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.parseFrom(s);
			case 60785748:
				return org.gof.demo.worldsrv.msg.Define.p_worker_pw_crops_info.parseFrom(s);
			case 1874350565:
				return org.gof.demo.worldsrv.msg.Define.p_worker_pw_food_info.parseFrom(s);
			case 1768865992:
				return org.gof.demo.worldsrv.msg.Define.p_yfs_pool.parseFrom(s);
			case 242099607:
				return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.parseFrom(s);
			case 242114967:
				return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.parseFrom(s);
			case -1382852968:
				return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.parseFrom(s);
			case -1382837608:
				return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.parseFrom(s);
			case 1042772898:
				return org.gof.demo.worldsrv.msg.MsgAct.act_accumulate_score_info_c2s.parseFrom(s);
			case 1042788258:
				return org.gof.demo.worldsrv.msg.MsgAct.act_accumulate_score_info_s2c.parseFrom(s);
			case 143262053:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_badge_c2s.parseFrom(s);
			case 143277413:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_badge_s2c.parseFrom(s);
			case -753365496:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_buy_c2s.parseFrom(s);
			case -753350136:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_buy_s2c.parseFrom(s);
			case 1089778111:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_choose_c2s.parseFrom(s);
			case 1089793471:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_choose_s2c.parseFrom(s);
			case -1317239780:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_combat_c2s.parseFrom(s);
			case -1317224420:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_combat_s2c.parseFrom(s);
			case 376092103:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_count_reward_c2s.parseFrom(s);
			case 376107463:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_count_reward_s2c.parseFrom(s);
			case 1100563141:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_dan_reward_c2s.parseFrom(s);
			case 1100578501:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_dan_reward_s2c.parseFrom(s);
			case -716566122:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_info_c2s.parseFrom(s);
			case -716550762:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_info_s2c.parseFrom(s);
			case 283012475:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_item_c2s.parseFrom(s);
			case 959160564:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_item_levup_c2s.parseFrom(s);
			case 959175924:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_item_levup_s2c.parseFrom(s);
			case 283027835:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_item_s2c.parseFrom(s);
			case 1014819731:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_lock_c2s.parseFrom(s);
			case 1014835091:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_lock_s2c.parseFrom(s);
			case -998924487:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_move_c2s.parseFrom(s);
			case -998909127:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_move_s2c.parseFrom(s);
			case -281962147:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_refresh_c2s.parseFrom(s);
			case -281946787:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_refresh_s2c.parseFrom(s);
			case 1891794220:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_reply_c2s.parseFrom(s);
			case 1891809580:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_reply_s2c.parseFrom(s);
			case -1505702715:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_result_s2c.parseFrom(s);
			case 303073850:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_sell_c2s.parseFrom(s);
			case 303089210:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_sell_s2c.parseFrom(s);
			case -1239800866:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_shop_c2s.parseFrom(s);
			case -1239785506:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_shop_s2c.parseFrom(s);
			case 495553287:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_badge_c2s.parseFrom(s);
			case 495568647:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_badge_s2c.parseFrom(s);
			case -874095523:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_choose_c2s.parseFrom(s);
			case -874080163:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_choose_s2c.parseFrom(s);
			case 1013853882:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_combat_c2s.parseFrom(s);
			case 1013869242:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_combat_s2c.parseFrom(s);
			case 1913483749:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_count_reward_c2s.parseFrom(s);
			case 1913499109:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_count_reward_s2c.parseFrom(s);
			case 1347972707:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_dan_reward_c2s.parseFrom(s);
			case 1347988067:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_dan_reward_s2c.parseFrom(s);
			case -12465228:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_info_c2s.parseFrom(s);
			case -12449868:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_info_s2c.parseFrom(s);
			case 987113369:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_item_c2s.parseFrom(s);
			case 1206570130:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_item_levup_c2s.parseFrom(s);
			case 1206585490:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_item_levup_s2c.parseFrom(s);
			case 987128729:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_item_s2c.parseFrom(s);
			case 1718920625:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_lock_c2s.parseFrom(s);
			case 1718935985:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_lock_s2c.parseFrom(s);
			case -294823593:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_move_c2s.parseFrom(s);
			case -294808233:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_move_s2c.parseFrom(s);
			case -2096752627:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_null_c2s.parseFrom(s);
			case -2096737267:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_null_s2c.parseFrom(s);
			case 1217653781:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_pet_skill_c2s.parseFrom(s);
			case 1217669141:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_pet_skill_s2c.parseFrom(s);
			case 1585578016:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_push_c2s.parseFrom(s);
			case 1585593376:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_push_s2c.parseFrom(s);
			case -1032502657:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_refresh_c2s.parseFrom(s);
			case -1032487297:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_refresh_s2c.parseFrom(s);
			case -2050881842:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_reply_c2s.parseFrom(s);
			case -2050866482:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_reply_s2c.parseFrom(s);
			case 825390947:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_result_s2c.parseFrom(s);
			case 1222578040:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_set_pet_skill_c2s.parseFrom(s);
			case 1222593400:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_set_pet_skill_s2c.parseFrom(s);
			case -1193444674:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_total_sp_c2s.parseFrom(s);
			case -1193429314:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_total_sp_s2c.parseFrom(s);
			case 1132090324:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_total_sp_update_c2s.parseFrom(s);
			case 1132105684:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_stable_total_sp_update_s2c.parseFrom(s);
			case 598474656:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_total_sp_c2s.parseFrom(s);
			case 598490016:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_total_sp_s2c.parseFrom(s);
			case -2066158414:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_total_sp_update_c2s.parseFrom(s);
			case -2066143054:
				return org.gof.demo.worldsrv.msg.MsgAct.act_adventure_space_total_sp_update_s2c.parseFrom(s);
			case -502972670:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_del_c2s.parseFrom(s);
			case -502957310:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_del_s2c.parseFrom(s);
			case -52467753:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_draw_c2s.parseFrom(s);
			case -52452393:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_draw_s2c.parseFrom(s);
			case 952359489:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_info_c2s.parseFrom(s);
			case 952374849:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_info_s2c.parseFrom(s);
			case 269774301:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_open_c2s.parseFrom(s);
			case 269789661:
				return org.gof.demo.worldsrv.msg.MsgAct.act_airdrop_gift_open_s2c.parseFrom(s);
			case -231580486:
				return org.gof.demo.worldsrv.msg.MsgAct.act_armor_acquire_hurt_add_c2s.parseFrom(s);
			case -231565126:
				return org.gof.demo.worldsrv.msg.MsgAct.act_armor_acquire_hurt_add_s2c.parseFrom(s);
			case 1720684920:
				return org.gof.demo.worldsrv.msg.MsgAct.act_armor_acquire_reward_c2s.parseFrom(s);
			case 1720700280:
				return org.gof.demo.worldsrv.msg.MsgAct.act_armor_acquire_reward_s2c.parseFrom(s);
			case 493482627:
				return org.gof.demo.worldsrv.msg.MsgAct.act_armor_acquire_reward_status_c2s.parseFrom(s);
			case 493497987:
				return org.gof.demo.worldsrv.msg.MsgAct.act_armor_acquire_reward_status_s2c.parseFrom(s);
			case -1173594337:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_history_c2s.parseFrom(s);
			case -1173578977:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_history_s2c.parseFrom(s);
			case -2079060563:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_info_c2s.parseFrom(s);
			case -2079045203:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_info_s2c.parseFrom(s);
			case 307397531:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_point_c2s.parseFrom(s);
			case 307412891:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_point_s2c.parseFrom(s);
			case 1696366073:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_shot_c2s.parseFrom(s);
			case 1696381433:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_angry_bird_shot_s2c.parseFrom(s);
			case -701194062:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_guess_choose_c2s.parseFrom(s);
			case -701178702:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_guess_choose_s2c.parseFrom(s);
			case -410050231:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_guess_info_c2s.parseFrom(s);
			case -410034871:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_guess_info_s2c.parseFrom(s);
			case -454112643:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_pig_find_c2s.parseFrom(s);
			case -454097283:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_pig_find_s2c.parseFrom(s);
			case 403027698:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_pig_info_c2s.parseFrom(s);
			case 403043058:
				return org.gof.demo.worldsrv.msg.MsgAct.act_autumn_pig_info_s2c.parseFrom(s);
			case -1879855041:
				return org.gof.demo.worldsrv.msg.MsgAct.act_bawang_get_reward_c2s.parseFrom(s);
			case -1879839681:
				return org.gof.demo.worldsrv.msg.MsgAct.act_bawang_get_reward_s2c.parseFrom(s);
			case -939451595:
				return org.gof.demo.worldsrv.msg.MsgAct.act_bawang_info_c2s.parseFrom(s);
			case -939436235:
				return org.gof.demo.worldsrv.msg.MsgAct.act_bawang_info_s2c.parseFrom(s);
			case 1785918083:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_auto_next_c2s.parseFrom(s);
			case 1785933443:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_auto_next_s2c.parseFrom(s);
			case -365847784:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_info_c2s.parseFrom(s);
			case -365832424:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_info_s2c.parseFrom(s);
			case 880613085:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_next_c2s.parseFrom(s);
			case -1048432972:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_open_c2s.parseFrom(s);
			case -741461059:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_open_fifty_c2s.parseFrom(s);
			case -741445699:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_open_fifty_s2c.parseFrom(s);
			case -1048417612:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_open_s2c.parseFrom(s);
			case -1663369326:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_open_ten_c2s.parseFrom(s);
			case -1663353966:
				return org.gof.demo.worldsrv.msg.MsgAct.act_box_tower_open_ten_s2c.parseFrom(s);
			case -988177626:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_break_c2s.parseFrom(s);
			case -988162266:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_break_s2c.parseFrom(s);
			case 152310778:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_choose_c2s.parseFrom(s);
			case 152326138:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_choose_s2c.parseFrom(s);
			case -1732065647:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_info_c2s.parseFrom(s);
			case -1732050287:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_info_s2c.parseFrom(s);
			case -485604778:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_next_c2s.parseFrom(s);
			case -485589418:
				return org.gof.demo.worldsrv.msg.MsgAct.act_break_gold_egg_next_s2c.parseFrom(s);
			case -745920981:
				return org.gof.demo.worldsrv.msg.MsgAct.act_broadcast_c2s.parseFrom(s);
			case -745905621:
				return org.gof.demo.worldsrv.msg.MsgAct.act_broadcast_s2c.parseFrom(s);
			case -795979527:
				return org.gof.demo.worldsrv.msg.MsgAct.act_calendar_info_s2c.parseFrom(s);
			case 1735718266:
				return org.gof.demo.worldsrv.msg.MsgAct.act_calendar_reward_c2s.parseFrom(s);
			case 1735733626:
				return org.gof.demo.worldsrv.msg.MsgAct.act_calendar_reward_s2c.parseFrom(s);
			case -1997415530:
				return org.gof.demo.worldsrv.msg.MsgAct.act_camp_info_c2s.parseFrom(s);
			case -1997400170:
				return org.gof.demo.worldsrv.msg.MsgAct.act_camp_info_s2c.parseFrom(s);
			case 717862418:
				return org.gof.demo.worldsrv.msg.MsgAct.act_camp_join_c2s.parseFrom(s);
			case -1778284329:
				return org.gof.demo.worldsrv.msg.MsgAct.act_camp_reward_c2s.parseFrom(s);
			case -2041191855:
				return org.gof.demo.worldsrv.msg.MsgAct.act_camp_support_c2s.parseFrom(s);
			case -2041176495:
				return org.gof.demo.worldsrv.msg.MsgAct.act_camp_support_s2c.parseFrom(s);
			case 527716362:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_ask_c2s.parseFrom(s);
			case 527731722:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_ask_s2c.parseFrom(s);
			case 1385730122:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_chat_status_c2s.parseFrom(s);
			case 1385745482:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_chat_status_s2c.parseFrom(s);
			case 841806241:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_chat_update_s2c.parseFrom(s);
			case -34075673:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_info_c2s.parseFrom(s);
			case -34060313:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_info_s2c.parseFrom(s);
			case 284314368:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_msg_update_s2c.parseFrom(s);
			case -1005673185:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_recv_c2s.parseFrom(s);
			case -1284223938:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_search_role_c2s.parseFrom(s);
			case -1284208578:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_search_role_s2c.parseFrom(s);
			case 1035434433:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_send_c2s.parseFrom(s);
			case 1035449793:
				return org.gof.demo.worldsrv.msg.MsgAct.act_card_send_s2c.parseFrom(s);
			case -179291877:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_gift_list_c2s.parseFrom(s);
			case -179276517:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_gift_list_s2c.parseFrom(s);
			case -427143898:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_open_c2s.parseFrom(s);
			case -427128538:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_open_s2c.parseFrom(s);
			case 1250958501:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_role_info_c2s.parseFrom(s);
			case 1250973861:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_role_info_s2c.parseFrom(s);
			case 1324951396:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_send_c2s.parseFrom(s);
			case 810913325:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_share_c2s.parseFrom(s);
			case 810928685:
				return org.gof.demo.worldsrv.msg.MsgAct.act_christmas_gift_share_s2c.parseFrom(s);
			case -1956200159:
				return org.gof.demo.worldsrv.msg.MsgAct.act_code_invite_bind_c2s.parseFrom(s);
			case -1956184799:
				return org.gof.demo.worldsrv.msg.MsgAct.act_code_invite_bind_s2c.parseFrom(s);
			case 1577214226:
				return org.gof.demo.worldsrv.msg.MsgAct.act_code_invite_info_c2s.parseFrom(s);
			case 1577229586:
				return org.gof.demo.worldsrv.msg.MsgAct.act_code_invite_info_s2c.parseFrom(s);
			case -1163798619:
				return org.gof.demo.worldsrv.msg.MsgAct.act_code_invite_share_c2s.parseFrom(s);
			case -1163783259:
				return org.gof.demo.worldsrv.msg.MsgAct.act_code_invite_share_s2c.parseFrom(s);
			case 2065221878:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_ask_c2s.parseFrom(s);
			case 2065237238:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_ask_s2c.parseFrom(s);
			case -1899720772:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_c2s.parseFrom(s);
			case -1923392202:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_chat_status_c2s.parseFrom(s);
			case -1923376842:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_chat_status_s2c.parseFrom(s);
			case 1827651213:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_chat_update_s2c.parseFrom(s);
			case 1701589140:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_msg_update_s2c.parseFrom(s);
			case -587642445:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_recv_c2s.parseFrom(s);
			case -587627085:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_recv_s2c.parseFrom(s);
			case -1899705412:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_s2c.parseFrom(s);
			case 1453465173:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_send_c2s.parseFrom(s);
			case 1453480533:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_blessing_send_s2c.parseFrom(s);
			case 1539760465:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_five_blessing_luck_c2s.parseFrom(s);
			case -1738450275:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_five_blessing_show_c2s.parseFrom(s);
			case -1738434915:
				return org.gof.demo.worldsrv.msg.MsgAct.act_collect_five_blessing_show_s2c.parseFrom(s);
			case 2062264583:
				return org.gof.demo.worldsrv.msg.MsgAct.act_day_pay_choose_reward_c2s.parseFrom(s);
			case 1248199858:
				return org.gof.demo.worldsrv.msg.MsgAct.act_day_pay_get_reward_c2s.parseFrom(s);
			case 169655784:
				return org.gof.demo.worldsrv.msg.MsgAct.act_day_pay_info_s2c.parseFrom(s);
			case -92165467:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_fashion_claim_c2s.parseFrom(s);
			case -92150107:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_fashion_claim_s2c.parseFrom(s);
			case 877739919:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_fashion_info_c2s.parseFrom(s);
			case 877755279:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_fashion_info_s2c.parseFrom(s);
			case -1281672985:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_battlepass_claim_c2s.parseFrom(s);
			case 1116463373:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_battlepass_info_c2s.parseFrom(s);
			case 1116478733:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_battlepass_info_s2c.parseFrom(s);
			case 1140358211:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_finish_order_c2s.parseFrom(s);
			case 1140373571:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_finish_order_s2c.parseFrom(s);
			case -941262257:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_info_c2s.parseFrom(s);
			case -941246897:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_info_s2c.parseFrom(s);
			case -1223620622:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_move_c2s.parseFrom(s);
			case -1223605262:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_move_s2c.parseFrom(s);
			case -1623847445:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_open_c2s.parseFrom(s);
			case -1623832085:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_open_s2c.parseFrom(s);
			case 78377715:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_sell_c2s.parseFrom(s);
			case 78393075:
				return org.gof.demo.worldsrv.msg.MsgAct.act_digimon_merge_sell_s2c.parseFrom(s);
			case -210457980:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_choose_c2s.parseFrom(s);
			case -210442620:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_choose_s2c.parseFrom(s);
			case 1815889612:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_count_reward_c2s.parseFrom(s);
			case 1815904972:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_count_reward_s2c.parseFrom(s);
			case -1767439055:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_draw_c2s.parseFrom(s);
			case -1767423695:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_draw_s2c.parseFrom(s);
			case -762611813:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_info_c2s.parseFrom(s);
			case -762596453:
				return org.gof.demo.worldsrv.msg.MsgAct.act_double_draw_info_s2c.parseFrom(s);
			case 782678811:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_battlepass_claim_c2s.parseFrom(s);
			case -340965287:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_battlepass_info_c2s.parseFrom(s);
			case -340949927:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_battlepass_info_s2c.parseFrom(s);
			case 1707937037:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_get_reward_c2s.parseFrom(s);
			case 1707952397:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_get_reward_s2c.parseFrom(s);
			case -2084924243:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_handbook_c2s.parseFrom(s);
			case 267962068:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_handbook_fish_c2s.parseFrom(s);
			case 267977428:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_handbook_fish_s2c.parseFrom(s);
			case 1695117931:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_handbook_reward_c2s.parseFrom(s);
			case 1695133291:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_handbook_reward_s2c.parseFrom(s);
			case -2084908883:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_handbook_s2c.parseFrom(s);
			case -535057789:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_info_c2s.parseFrom(s);
			case -535042429:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_info_s2c.parseFrom(s);
			case -1832151699:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_levup_c2s.parseFrom(s);
			case -1832136339:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_levup_s2c.parseFrom(s);
			case 1125098098:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_result_c2s.parseFrom(s);
			case 1125113458:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_result_s2c.parseFrom(s);
			case 1527986007:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_start_c2s.parseFrom(s);
			case 1528001367:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_start_s2c.parseFrom(s);
			case -397448409:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_turntable_draw_c2s.parseFrom(s);
			case -397433049:
				return org.gof.demo.worldsrv.msg.MsgAct.act_fishing_turntable_draw_s2c.parseFrom(s);
			case -214287079:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_bird_info_c2s.parseFrom(s);
			case -214271719:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_bird_info_s2c.parseFrom(s);
			case 148105096:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_bird_result_c2s.parseFrom(s);
			case 148120456:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_bird_result_s2c.parseFrom(s);
			case -1413023871:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_bird_start_c2s.parseFrom(s);
			case -1413008511:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_bird_start_s2c.parseFrom(s);
			case -527982910:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_cat_buy_c2s.parseFrom(s);
			case -426712136:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_cat_claim_c2s.parseFrom(s);
			case 1975326748:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_cat_info_c2s.parseFrom(s);
			case 1975342108:
				return org.gof.demo.worldsrv.msg.MsgAct.act_frog_cat_info_s2c.parseFrom(s);
			case -1909918014:
				return org.gof.demo.worldsrv.msg.MsgAct.act_gold_egg_count_reward_c2s.parseFrom(s);
			case -1909902654:
				return org.gof.demo.worldsrv.msg.MsgAct.act_gold_egg_count_reward_s2c.parseFrom(s);
			case 316196976:
				return org.gof.demo.worldsrv.msg.MsgAct.act_group_gift_c2s.parseFrom(s);
			case -734982428:
				return org.gof.demo.worldsrv.msg.MsgAct.act_group_gift_opt_c2s.parseFrom(s);
			case 882969928:
				return org.gof.demo.worldsrv.msg.MsgAct.act_group_gift_reward_c2s.parseFrom(s);
			case 882985288:
				return org.gof.demo.worldsrv.msg.MsgAct.act_group_gift_reward_s2c.parseFrom(s);
			case 316212336:
				return org.gof.demo.worldsrv.msg.MsgAct.act_group_gift_s2c.parseFrom(s);
			case 1172581045:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_buff_update_s2c.parseFrom(s);
			case 734112278:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_buy_buff_c2s.parseFrom(s);
			case -822366508:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_buy_pass_c2s.parseFrom(s);
			case -822351148:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_buy_pass_s2c.parseFrom(s);
			case -1904856325:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_choose_buff_c2s.parseFrom(s);
			case -1904840965:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_choose_buff_s2c.parseFrom(s);
			case -1969437730:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_combat_c2s.parseFrom(s);
			case -1969422370:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_combat_s2c.parseFrom(s);
			case 2107333080:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_info_c2s.parseFrom(s);
			case 2107348440:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_info_s2c.parseFrom(s);
			case 2137051271:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_result_c2s.parseFrom(s);
			case 2137066631:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_result_s2c.parseFrom(s);
			case -1088037613:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_trick_c2s.parseFrom(s);
			case -1088022253:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_arena_trick_s2c.parseFrom(s);
			case -1962287041:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_bundle_data_c2s.parseFrom(s);
			case -1962271681:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_bundle_data_s2c.parseFrom(s);
			case -1013519496:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_chat_status_c2s.parseFrom(s);
			case -1013504136:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_chat_status_s2c.parseFrom(s);
			case 222393717:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_data_c2s.parseFrom(s);
			case 222409077:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_data_s2c.parseFrom(s);
			case -1388722055:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_info_c2s.parseFrom(s);
			case -1388706695:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_info_s2c.parseFrom(s);
			case 1676922266:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_quit_c2s.parseFrom(s);
			case 1676937626:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_quit_s2c.parseFrom(s);
			case 1381457182:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_share_c2s.parseFrom(s);
			case 1381472542:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_share_s2c.parseFrom(s);
			case 834197537:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_start_c2s.parseFrom(s);
			case 834212897:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_group_buy_start_s2c.parseFrom(s);
			case 697687462:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_match_3_info_c2s.parseFrom(s);
			case 697702822:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_match_3_info_s2c.parseFrom(s);
			case 382310613:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_match_3_result_c2s.parseFrom(s);
			case 382325973:
				return org.gof.demo.worldsrv.msg.MsgAct.act_halloween_match_3_result_s2c.parseFrom(s);
			case 979135264:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_change_idol_c2s.parseFrom(s);
			case 979150624:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_change_idol_s2c.parseFrom(s);
			case 49403481:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_champion_get_reward_c2s.parseFrom(s);
			case 49418841:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_champion_get_reward_s2c.parseFrom(s);
			case 1169448143:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_champion_info_c2s.parseFrom(s);
			case 1169463503:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_champion_info_s2c.parseFrom(s);
			case -1522192962:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_champion_result_s2c.parseFrom(s);
			case 1390045813:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_copy_boss_info_c2s.parseFrom(s);
			case 1390061173:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_copy_boss_info_s2c.parseFrom(s);
			case -1065978353:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_copy_inspire_c2s.parseFrom(s);
			case -1065962993:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_main_copy_inspire_s2c.parseFrom(s);
			case 1494138290:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_pick_c2s.parseFrom(s);
			case 1494153650:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_pick_s2c.parseFrom(s);
			case -2073220946:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_story_c2s.parseFrom(s);
			case 770280266:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_story_reward_c2s.parseFrom(s);
			case 770295626:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_story_reward_s2c.parseFrom(s);
			case -2073205586:
				return org.gof.demo.worldsrv.msg.MsgAct.act_idol_story_s2c.parseFrom(s);
			case -1273885413:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_ask_c2s.parseFrom(s);
			case -1273870053:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_ask_s2c.parseFrom(s);
			case 1488235611:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_chat_status_c2s.parseFrom(s);
			case 1488250971:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_chat_status_s2c.parseFrom(s);
			case 944311730:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_chat_update_s2c.parseFrom(s);
			case -49155850:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_info_c2s.parseFrom(s);
			case -49140490:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_info_s2c.parseFrom(s);
			case -1513494321:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_msg_update_s2c.parseFrom(s);
			case -1020753362:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_recv_c2s.parseFrom(s);
			case -1020738002:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_recv_s2c.parseFrom(s);
			case -1181718449:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_search_role_c2s.parseFrom(s);
			case -1181703089:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_search_role_s2c.parseFrom(s);
			case 1020354256:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_send_c2s.parseFrom(s);
			case 1020369616:
				return org.gof.demo.worldsrv.msg.MsgAct.act_jigsaw_send_s2c.parseFrom(s);
			case -620628243:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_buff_active_c2s.parseFrom(s);
			case 1334465328:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_buff_update_s2c.parseFrom(s);
			case -818753238:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_buff_use_c2s.parseFrom(s);
			case 949188331:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_collection_reward_c2s.parseFrom(s);
			case 949203691:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_collection_reward_s2c.parseFrom(s);
			case -1998194937:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_collection_reward_update_s2c.parseFrom(s);
			case -2092678121:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_energy_c2s.parseFrom(s);
			case -2092662761:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_energy_s2c.parseFrom(s);
			case 1783494077:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_info_c2s.parseFrom(s);
			case 1783509437:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_info_s2c.parseFrom(s);
			case -293173804:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_invasion_c2s.parseFrom(s);
			case -293158444:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_invasion_s2c.parseFrom(s);
			case -149989325:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_merge_c2s.parseFrom(s);
			case -781476398:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_power_update_s2c.parseFrom(s);
			case -1012136169:
				return org.gof.demo.worldsrv.msg.MsgAct.act_legion_step_update_s2c.parseFrom(s);
			case 849391783:
				return org.gof.demo.worldsrv.msg.MsgAct.act_limited_rank_reward_c2s.parseFrom(s);
			case 849407143:
				return org.gof.demo.worldsrv.msg.MsgAct.act_limited_rank_reward_s2c.parseFrom(s);
			case -2126938274:
				return org.gof.demo.worldsrv.msg.MsgAct.act_list_c2s.parseFrom(s);
			case -2126922914:
				return org.gof.demo.worldsrv.msg.MsgAct.act_list_s2c.parseFrom(s);
			case 1741112132:
				return org.gof.demo.worldsrv.msg.MsgAct.act_login_info_c2s.parseFrom(s);
			case 1741127492:
				return org.gof.demo.worldsrv.msg.MsgAct.act_login_info_s2c.parseFrom(s);
			case 354139397:
				return org.gof.demo.worldsrv.msg.MsgAct.act_login_reward_c2s.parseFrom(s);
			case 354154757:
				return org.gof.demo.worldsrv.msg.MsgAct.act_login_reward_s2c.parseFrom(s);
			case -1592720279:
				return org.gof.demo.worldsrv.msg.MsgAct.act_lucky_cat_c2s.parseFrom(s);
			case -1384833042:
				return org.gof.demo.worldsrv.msg.MsgAct.act_lucky_cat_info_c2s.parseFrom(s);
			case -1384817682:
				return org.gof.demo.worldsrv.msg.MsgAct.act_lucky_cat_info_s2c.parseFrom(s);
			case -1256877388:
				return org.gof.demo.worldsrv.msg.MsgAct.act_lucky_cat_report_c2s.parseFrom(s);
			case -1256862028:
				return org.gof.demo.worldsrv.msg.MsgAct.act_lucky_cat_report_s2c.parseFrom(s);
			case -1592704919:
				return org.gof.demo.worldsrv.msg.MsgAct.act_lucky_cat_s2c.parseFrom(s);
			case -969441933:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mini_game_info_c2s.parseFrom(s);
			case -969426573:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mini_game_info_s2c.parseFrom(s);
			case 293763426:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mini_game_result_c2s.parseFrom(s);
			case 293778786:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mini_game_result_s2c.parseFrom(s);
			case 946979431:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mini_game_start_c2s.parseFrom(s);
			case 946994791:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mini_game_start_s2c.parseFrom(s);
			case -1907431065:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_info_c2s.parseFrom(s);
			case -1907415705:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_info_s2c.parseFrom(s);
			case -1514778100:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_inherit_c2s.parseFrom(s);
			case -1514762740:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_inherit_s2c.parseFrom(s);
			case 829339734:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_result_c2s.parseFrom(s);
			case 829355094:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_result_s2c.parseFrom(s);
			case 1474280068:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_settle_c2s.parseFrom(s);
			case 1474295428:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mining_settle_s2c.parseFrom(s);
			case 2065956360:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_choose_c2s.parseFrom(s);
			case 546826064:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_count_reward_c2s.parseFrom(s);
			case 546841424:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_count_reward_s2c.parseFrom(s);
			case 844982709:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_draw_c2s.parseFrom(s);
			case 844998069:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_draw_s2c.parseFrom(s);
			case 1849809951:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_info_c2s.parseFrom(s);
			case 1849825311:
				return org.gof.demo.worldsrv.msg.MsgAct.act_mount_carnival_info_s2c.parseFrom(s);
			case -1029549292:
				return org.gof.demo.worldsrv.msg.MsgAct.act_newyear_dinner_c2s.parseFrom(s);
			case -238308512:
				return org.gof.demo.worldsrv.msg.MsgAct.act_newyear_dinner_enjoy_c2s.parseFrom(s);
			case -238293152:
				return org.gof.demo.worldsrv.msg.MsgAct.act_newyear_dinner_enjoy_s2c.parseFrom(s);
			case 337659235:
				return org.gof.demo.worldsrv.msg.MsgAct.act_newyear_dinner_make_c2s.parseFrom(s);
			case 337674595:
				return org.gof.demo.worldsrv.msg.MsgAct.act_newyear_dinner_make_s2c.parseFrom(s);
			case -1029533932:
				return org.gof.demo.worldsrv.msg.MsgAct.act_newyear_dinner_s2c.parseFrom(s);
			case -437711257:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pay_rebate_get_reward_c2s.parseFrom(s);
			case -1606273187:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pay_rebate_info_c2s.parseFrom(s);
			case -1606257827:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pay_rebate_info_s2c.parseFrom(s);
			case -1351888906:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pillow_info_c2s.parseFrom(s);
			case -1351873546:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pillow_info_s2c.parseFrom(s);
			case 2120420175:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pillow_take_c2s.parseFrom(s);
			case 2120435535:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pillow_take_s2c.parseFrom(s);
			case -1445740344:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pillow_throw_c2s.parseFrom(s);
			case -1445724984:
				return org.gof.demo.worldsrv.msg.MsgAct.act_pillow_throw_s2c.parseFrom(s);
			case 724465251:
				return org.gof.demo.worldsrv.msg.MsgAct.act_refresh_gift_info_c2s.parseFrom(s);
			case 724480611:
				return org.gof.demo.worldsrv.msg.MsgAct.act_refresh_gift_info_s2c.parseFrom(s);
			case 1285547376:
				return org.gof.demo.worldsrv.msg.MsgAct.act_refresh_gift_refresh_c2s.parseFrom(s);
			case 1285562736:
				return org.gof.demo.worldsrv.msg.MsgAct.act_refresh_gift_refresh_s2c.parseFrom(s);
			case -1685937052:
				return org.gof.demo.worldsrv.msg.MsgAct.act_refresh_gift_reward_c2s.parseFrom(s);
			case 1760775991:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_chapter_pass_c2s.parseFrom(s);
			case 1760791351:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_chapter_pass_s2c.parseFrom(s);
			case 2137135215:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_get_day_reward_c2s.parseFrom(s);
			case 2137150575:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_get_day_reward_s2c.parseFrom(s);
			case -1984545298:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_get_time_reward_c2s.parseFrom(s);
			case -1984529938:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_get_time_reward_s2c.parseFrom(s);
			case 1650752674:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_info_c2s.parseFrom(s);
			case 1650768034:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_info_s2c.parseFrom(s);
			case -1247768895:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_monster_equip_c2s.parseFrom(s);
			case -1247753535:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_monster_equip_s2c.parseFrom(s);
			case 120510205:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_monster_unlock_c2s.parseFrom(s);
			case 120525565:
				return org.gof.demo.worldsrv.msg.MsgAct.act_reverse_war_monster_unlock_s2c.parseFrom(s);
			case -998894321:
				return org.gof.demo.worldsrv.msg.MsgAct.act_s1_preview_time_c2s.parseFrom(s);
			case -998878961:
				return org.gof.demo.worldsrv.msg.MsgAct.act_s1_preview_time_s2c.parseFrom(s);
			case 1800407031:
				return org.gof.demo.worldsrv.msg.MsgAct.act_server_puzzle_claim_reward_c2s.parseFrom(s);
			case 1800422391:
				return org.gof.demo.worldsrv.msg.MsgAct.act_server_puzzle_claim_reward_s2c.parseFrom(s);
			case 806999571:
				return org.gof.demo.worldsrv.msg.MsgAct.act_server_puzzle_info_c2s.parseFrom(s);
			case 807014931:
				return org.gof.demo.worldsrv.msg.MsgAct.act_server_puzzle_info_s2c.parseFrom(s);
			case -929105405:
				return org.gof.demo.worldsrv.msg.MsgAct.act_server_puzzle_use_goods_c2s.parseFrom(s);
			case 1543274149:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_angry_bird_info_c2s.parseFrom(s);
			case 1543289509:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_angry_bird_info_s2c.parseFrom(s);
			case 1242297876:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_angry_bird_result_c2s.parseFrom(s);
			case 1242313236:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_angry_bird_result_s2c.parseFrom(s);
			case 942464267:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_claim_c2s.parseFrom(s);
			case 942479627:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_claim_s2c.parseFrom(s);
			case -1998378903:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_info_c2s.parseFrom(s);
			case -1998363543:
				return org.gof.demo.worldsrv.msg.MsgAct.act_seven_trial_info_s2c.parseFrom(s);
			case -1598594402:
				return org.gof.demo.worldsrv.msg.MsgAct.act_skin_try_info_c2s.parseFrom(s);
			case -1598579042:
				return org.gof.demo.worldsrv.msg.MsgAct.act_skin_try_info_s2c.parseFrom(s);
			case -174813621:
				return org.gof.demo.worldsrv.msg.MsgAct.act_skin_try_pop_s2c.parseFrom(s);
			case 72080001:
				return org.gof.demo.worldsrv.msg.MsgAct.act_skin_try_use_c2s.parseFrom(s);
			case 72095361:
				return org.gof.demo.worldsrv.msg.MsgAct.act_skin_try_use_s2c.parseFrom(s);
			case 1552659766:
				return org.gof.demo.worldsrv.msg.MsgAct.act_sns_share_reward_c2s.parseFrom(s);
			case -1258749676:
				return org.gof.demo.worldsrv.msg.MsgAct.act_sns_share_state_s2c.parseFrom(s);
			case -1381023319:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_battle_pass_claim_c2s.parseFrom(s);
			case -1381007959:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_battle_pass_claim_s2c.parseFrom(s);
			case -966987942:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_discount_mall_choose_c2s.parseFrom(s);
			case -966972582:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_discount_mall_choose_s2c.parseFrom(s);
			case -682952207:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_discount_mall_info_c2s.parseFrom(s);
			case -682936847:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_discount_mall_info_s2c.parseFrom(s);
			case 2022637385:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_dungeon_info_c2s.parseFrom(s);
			case 2022652745:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_dungeon_info_s2c.parseFrom(s);
			case -880133669:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_hour_reward_claim_c2s.parseFrom(s);
			case -880118309:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_hour_reward_claim_s2c.parseFrom(s);
			case -1505427168:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_s_equip_use_c2s.parseFrom(s);
			case -1505411808:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_s_equip_use_s2c.parseFrom(s);
			case -1807624518:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_equip_c2s.parseFrom(s);
			case 663890582:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_equip_rebuild_c2s.parseFrom(s);
			case 663905942:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_equip_rebuild_s2c.parseFrom(s);
			case -1807609158:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_equip_s2c.parseFrom(s);
			case 639430990:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_info_c2s.parseFrom(s);
			case 639446350:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_info_s2c.parseFrom(s);
			case -591429369:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_job_c2s.parseFrom(s);
			case -591414009:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_job_s2c.parseFrom(s);
			case -244483058:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_level_s2c.parseFrom(s);
			case 168957289:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_pet_c2s.parseFrom(s);
			case 168972649:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_pet_s2c.parseFrom(s);
			case -1500027964:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_pet_unlock_c2s.parseFrom(s);
			case -892059557:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_skill_c2s.parseFrom(s);
			case -892044197:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_skill_s2c.parseFrom(s);
			case 198075538:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_tab_skill_unlock_c2s.parseFrom(s);
			case 485265417:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_total_sp_info_c2s.parseFrom(s);
			case 485280777:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_total_sp_info_s2c.parseFrom(s);
			case -1615874780:
				return org.gof.demo.worldsrv.msg.MsgAct.act_strategy_total_sp_update_s2c.parseFrom(s);
			case -1103234271:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_ask_list_c2s.parseFrom(s);
			case -1103218911:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_ask_list_s2c.parseFrom(s);
			case -1911153563:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_ask_teammate_c2s.parseFrom(s);
			case -1911138203:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_ask_teammate_s2c.parseFrom(s);
			case -49807708:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_battlepass_exp_s2c.parseFrom(s);
			case 575067569:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_battlepass_info_c2s.parseFrom(s);
			case 575082929:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_battlepass_info_s2c.parseFrom(s);
			case -1085511773:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_deal_ask_c2s.parseFrom(s);
			case -1085496413:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_deal_ask_s2c.parseFrom(s);
			case -1388655827:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_flower_history_c2s.parseFrom(s);
			case -1388640467:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_flower_history_s2c.parseFrom(s);
			case 480615769:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_flower_search_c2s.parseFrom(s);
			case 480631129:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_flower_search_s2c.parseFrom(s);
			case -814964247:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_flower_tips_s2c.parseFrom(s);
			case -409565003:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_get_reward_c2s.parseFrom(s);
			case -409549643:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_get_reward_s2c.parseFrom(s);
			case 1303130292:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_gift_tips_c2s.parseFrom(s);
			case -540009450:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_give_flower_c2s.parseFrom(s);
			case -539994090:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_give_flower_s2c.parseFrom(s);
			case 649794662:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_search_teammate_c2s.parseFrom(s);
			case 649810022:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tanabata_search_teammate_s2c.parseFrom(s);
			case 1455970029:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_choose_hammer_c2s.parseFrom(s);
			case 1455985389:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_choose_hammer_s2c.parseFrom(s);
			case 1527998265:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_info_c2s.parseFrom(s);
			case 1528013625:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_info_s2c.parseFrom(s);
			case -552924760:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_result_c2s.parseFrom(s);
			case -552909400:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_result_s2c.parseFrom(s);
			case 1058214241:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_start_c2s.parseFrom(s);
			case 1058229601:
				return org.gof.demo.worldsrv.msg.MsgAct.act_tang_mole_start_s2c.parseFrom(s);
			case -30571693:
				return org.gof.demo.worldsrv.msg.MsgAct.act_task_reward_c2s.parseFrom(s);
			case -30556333:
				return org.gof.demo.worldsrv.msg.MsgAct.act_task_reward_s2c.parseFrom(s);
			case -2017718291:
				return org.gof.demo.worldsrv.msg.MsgAct.act_task_update_c2s.parseFrom(s);
			case -2017702931:
				return org.gof.demo.worldsrv.msg.MsgAct.act_task_update_s2c.parseFrom(s);
			case -569799863:
				return org.gof.demo.worldsrv.msg.MsgAct.act_update_s2c.parseFrom(s);
			case -680939528:
				return org.gof.demo.worldsrv.msg.MsgAct.act_valentine_role_info_c2s.parseFrom(s);
			case -680924168:
				return org.gof.demo.worldsrv.msg.MsgAct.act_valentine_role_info_s2c.parseFrom(s);
			case 1913211338:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_add_reward_c2s.parseFrom(s);
			case 1913226698:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_add_reward_s2c.parseFrom(s);
			case -1355723259:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_close_s2c.parseFrom(s);
			case 704269163:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_info_c2s.parseFrom(s);
			case 704284523:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_info_s2c.parseFrom(s);
			case -1617847602:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_lev_reward_c2s.parseFrom(s);
			case -1617832242:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_lev_reward_s2c.parseFrom(s);
			case -1694802762:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_task_reward_c2s.parseFrom(s);
			case -1694787402:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_task_reward_s2c.parseFrom(s);
			case 169427912:
				return org.gof.demo.worldsrv.msg.MsgAct.act_war_token_update_task_s2c.parseFrom(s);
			case 821744882:
				return org.gof.demo.worldsrv.msg.MsgAct.act_week_card_info_c2s.parseFrom(s);
			case 821760242:
				return org.gof.demo.worldsrv.msg.MsgAct.act_week_card_info_s2c.parseFrom(s);
			case 1605475123:
				return org.gof.demo.worldsrv.msg.MsgAct.act_week_card_reward_c2s.parseFrom(s);
			case 1605490483:
				return org.gof.demo.worldsrv.msg.MsgAct.act_week_card_reward_s2c.parseFrom(s);
			case 1576136701:
				return org.gof.demo.worldsrv.msg.MsgAct.questionnaire_info_c2s.parseFrom(s);
			case 1576152061:
				return org.gof.demo.worldsrv.msg.MsgAct.questionnaire_info_s2c.parseFrom(s);
			case 753478486:
				return org.gof.demo.worldsrv.msg.MsgAct.questionnaire_take_c2s.parseFrom(s);
			case 753493846:
				return org.gof.demo.worldsrv.msg.MsgAct.questionnaire_take_s2c.parseFrom(s);
			case 455856916:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_bonfire_claim_reward_c2s.parseFrom(s);
			case 455872276:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_bonfire_claim_reward_s2c.parseFrom(s);
			case -1628631504:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_bonfire_info_c2s.parseFrom(s);
			case -1628616144:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_bonfire_info_s2c.parseFrom(s);
			case 1720404998:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_bonfire_use_goods_c2s.parseFrom(s);
			case 1720420358:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_bonfire_use_goods_s2c.parseFrom(s);
			case 164546285:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_candy_get_card_c2s.parseFrom(s);
			case 164561645:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_candy_get_card_s2c.parseFrom(s);
			case -1802870159:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_candy_result_c2s.parseFrom(s);
			case -1802854799:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_candy_result_s2c.parseFrom(s);
			case 1643957230:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_buff_refresh_c2s.parseFrom(s);
			case 1643972590:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_buff_refresh_s2c.parseFrom(s);
			case 1131744617:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_flip_c2s.parseFrom(s);
			case 1131759977:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_flip_s2c.parseFrom(s);
			case -932649425:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_game_over_c2s.parseFrom(s);
			case -932634065:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_game_over_s2c.parseFrom(s);
			case -541562582:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_info_c2s.parseFrom(s);
			case -541547222:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_info_s2c.parseFrom(s);
			case 528004742:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_round_complete_c2s.parseFrom(s);
			case 528020102:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_round_complete_s2c.parseFrom(s);
			case 1528878628:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_select_buff_c2s.parseFrom(s);
			case 1528893988:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_select_buff_s2c.parseFrom(s);
			case 1326337424:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_start_c2s.parseFrom(s);
			case 1326352784:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_card_eliminate_start_s2c.parseFrom(s);
			case -2045821327:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_attr_change_s2c.parseFrom(s);
			case 1522478377:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_element_reward_c2s.parseFrom(s);
			case 1522493737:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_element_reward_s2c.parseFrom(s);
			case -19340289:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_gamble_c2s.parseFrom(s);
			case -19324929:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_gamble_s2c.parseFrom(s);
			case -1375678331:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_info_c2s.parseFrom(s);
			case -1375662971:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_info_s2c.parseFrom(s);
			case -1658036696:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_move_c2s.parseFrom(s);
			case -1658021336:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_move_s2c.parseFrom(s);
			case 291265858:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_race_op_c2s.parseFrom(s);
			case -596706434:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_race_result_c2s.parseFrom(s);
			case -596691074:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_race_result_s2c.parseFrom(s);
			case -1618007768:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_race_s2c.parseFrom(s);
			case -1021392693:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_castle_race_start_s2c.parseFrom(s);
			case 2047982182:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_chat_reward_c2s.parseFrom(s);
			case 2047997542:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_chat_reward_s2c.parseFrom(s);
			case -1226299510:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_farm_claim_c2s.parseFrom(s);
			case -1226284150:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_farm_claim_s2c.parseFrom(s);
			case 979702282:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_farm_info_c2s.parseFrom(s);
			case 979717642:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_farm_info_s2c.parseFrom(s);
			case -1080923391:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_tree_decorate_c2s.parseFrom(s);
			case -1992026026:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_tree_decorate_info_c2s.parseFrom(s);
			case -1992010666:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_christmas_tree_decorate_info_s2c.parseFrom(s);
			case 1509585625:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_clear_game_info_c2s.parseFrom(s);
			case 1509600985:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_clear_game_info_s2c.parseFrom(s);
			case -740962264:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_clear_game_save_c2s.parseFrom(s);
			case 1627973225:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cohesion_get_reward_c2s.parseFrom(s);
			case -2044938017:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cohesion_info_c2s.parseFrom(s);
			case -2044922657:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cohesion_info_s2c.parseFrom(s);
			case 645848525:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_buy_c2s.parseFrom(s);
			case 645863885:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_buy_s2c.parseFrom(s);
			case 2052936719:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_claim_box_c2s.parseFrom(s);
			case -1379489784:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_claim_box_info_c2s.parseFrom(s);
			case -1379474424:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_claim_box_info_s2c.parseFrom(s);
			case 2052952079:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_claim_box_s2c.parseFrom(s);
			case -1951101949:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_claim_c2s.parseFrom(s);
			case -1951086589:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_claim_s2c.parseFrom(s);
			case -290604431:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_info_c2s.parseFrom(s);
			case -290589071:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_info_s2c.parseFrom(s);
			case 1389307309:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_throw_c2s.parseFrom(s);
			case 1389322669:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_cross_boss_throw_s2c.parseFrom(s);
			case 1784005721:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_buy_c2s.parseFrom(s);
			case 1784021081:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_buy_s2c.parseFrom(s);
			case 896270223:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_claim_c2s.parseFrom(s);
			case 896285583:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_claim_s2c.parseFrom(s);
			case 632530277:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_info_c2s.parseFrom(s);
			case 632545637:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_info_s2c.parseFrom(s);
			case 2052148447:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_search_c2s.parseFrom(s);
			case 2052163807:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_search_s2c.parseFrom(s);
			case 1702040383:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_send_c2s.parseFrom(s);
			case 1702055743:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_send_s2c.parseFrom(s);
			case -770984907:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_set_c2s.parseFrom(s);
			case -770969547:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_custom_mall_set_s2c.parseFrom(s);
			case 2093101620:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_divination_draw_c2s.parseFrom(s);
			case 2093116980:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_divination_draw_s2c.parseFrom(s);
			case -1197038434:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_divination_info_c2s.parseFrom(s);
			case -1197023074:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_divination_info_s2c.parseFrom(s);
			case -1879623622:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_divination_open_c2s.parseFrom(s);
			case -1879608262:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_divination_open_s2c.parseFrom(s);
			case -1980504108:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_draw_away_monster_c2s.parseFrom(s);
			case 343844707:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_draw_away_monster_info_c2s.parseFrom(s);
			case 343860067:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_draw_away_monster_info_s2c.parseFrom(s);
			case -1980488748:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_draw_away_monster_s2c.parseFrom(s);
			case -389117225:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_battle_c2s.parseFrom(s);
			case -389101865:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_battle_s2c.parseFrom(s);
			case -107280409:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_cook_c2s.parseFrom(s);
			case -107265049:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_cook_s2c.parseFrom(s);
			case -2056204405:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_craft_c2s.parseFrom(s);
			case 1312881164:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_craft_info_c2s.parseFrom(s);
			case 1312896524:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_craft_info_s2c.parseFrom(s);
			case -2056189045:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_craft_s2c.parseFrom(s);
			case -1886788106:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_cultivate_c2s.parseFrom(s);
			case -1886772746:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_cultivate_s2c.parseFrom(s);
			case 310387419:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_devour_stone_info_c2s.parseFrom(s);
			case 310402779:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_devour_stone_info_s2c.parseFrom(s);
			case -1962586994:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_devour_stone_op_c2s.parseFrom(s);
			case -1962571634:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_devour_stone_op_s2c.parseFrom(s);
			case 1175725776:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_enter_scene_c2s.parseFrom(s);
			case 1230586360:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_equip_upgrade_c2s.parseFrom(s);
			case 1230601720:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_equip_upgrade_s2c.parseFrom(s);
			case 123997438:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_explore_c2s.parseFrom(s);
			case 124012798:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_explore_s2c.parseFrom(s);
			case -1899627182:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_forge_c2s.parseFrom(s);
			case -1899611822:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_forge_s2c.parseFrom(s);
			case 618175053:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_info_c2s.parseFrom(s);
			case 618190413:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_info_s2c.parseFrom(s);
			case 567727473:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_receive_ore_c2s.parseFrom(s);
			case 567742833:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_receive_ore_s2c.parseFrom(s);
			case 678304160:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_round_change_s2c.parseFrom(s);
			case -401671904:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_scene_info_s2c.parseFrom(s);
			case 1925237226:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_settle_c2s.parseFrom(s);
			case -1745232115:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_settle_info_s2c.parseFrom(s);
			case -1563863465:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_sweep_c2s.parseFrom(s);
			case -1563848105:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_sweep_s2c.parseFrom(s);
			case 458438462:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_trigger_event_c2s.parseFrom(s);
			case 458453822:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_trigger_event_s2c.parseFrom(s);
			case 251332515:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_update_level_and_exp_s2c.parseFrom(s);
			case -899748712:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_update_stamina_s2c.parseFrom(s);
			case 1818787050:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_use_item_c2s.parseFrom(s);
			case 1818802410:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_dungeon_use_item_s2c.parseFrom(s);
			case 1273899865:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_end_c2s.parseFrom(s);
			case 1273915225:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_end_s2c.parseFrom(s);
			case -710542438:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_info_c2s.parseFrom(s);
			case -710527078:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_info_s2c.parseFrom(s);
			case 750638089:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_stamina_cost_c2s.parseFrom(s);
			case 426334671:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_state_c2s.parseFrom(s);
			case 1239052215:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_fruit_merge_use_item_c2s.parseFrom(s);
			case 1793529459:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_choose_c2s.parseFrom(s);
			case 1793544819:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_choose_s2c.parseFrom(s);
			case -1545354373:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_count_reward_c2s.parseFrom(s);
			case -1545339013:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_count_reward_s2c.parseFrom(s);
			case 1313972448:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_draw_c2s.parseFrom(s);
			case 1313987808:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_draw_s2c.parseFrom(s);
			case -1976167606:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_info_c2s.parseFrom(s);
			case -1976152246:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_golden_tower_info_s2c.parseFrom(s);
			case 1384588341:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_guild_pay_get_reward_c2s.parseFrom(s);
			case 190857131:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_guild_pay_info_c2s.parseFrom(s);
			case 190872491:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_guild_pay_info_s2c.parseFrom(s);
			case -271652418:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_jump_result_c2s.parseFrom(s);
			case -271637058:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_jump_result_s2c.parseFrom(s);
			case 1073997360:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_luck_koi_reward_info_c2s.parseFrom(s);
			case 1074012720:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_luck_koi_reward_info_s2c.parseFrom(s);
			case 1806147067:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_luck_slot_reward_info_c2s.parseFrom(s);
			case 1806162427:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_luck_slot_reward_info_s2c.parseFrom(s);
			case -1391815005:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_battle_result_c2s.parseFrom(s);
			case -1391799645:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_battle_result_s2c.parseFrom(s);
			case 1723889926:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_battle_start_c2s.parseFrom(s);
			case 1723905286:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_battle_start_s2c.parseFrom(s);
			case -1685445785:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_claim_box_c2s.parseFrom(s);
			case -1685430425:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_claim_box_s2c.parseFrom(s);
			case -2000833013:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_drug_c2s.parseFrom(s);
			case -2000817653:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_drug_s2c.parseFrom(s);
			case 616874934:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_farm_info_c2s.parseFrom(s);
			case 616890294:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_farm_info_s2c.parseFrom(s);
			case -1475695465:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_rank_c2s.parseFrom(s);
			case -1475680105:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_rank_s2c.parseFrom(s);
			case 558622078:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_share_c2s.parseFrom(s);
			case 558637438:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mashle_alchemy_share_s2c.parseFrom(s);
			case -178326480:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mine_sweeper_info_c2s.parseFrom(s);
			case -178311120:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mine_sweeper_info_s2c.parseFrom(s);
			case -860911668:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mine_sweeper_open_c2s.parseFrom(s);
			case -860896308:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mine_sweeper_open_s2c.parseFrom(s);
			case -125532794:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mini_game_buy_info_c2s.parseFrom(s);
			case -125517434:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mini_game_buy_info_s2c.parseFrom(s);
			case 874045803:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mini_game_buy_item_c2s.parseFrom(s);
			case 874061163:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_mini_game_buy_item_s2c.parseFrom(s);
			case 984984040:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_circle_reward_c2s.parseFrom(s);
			case 984999400:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_circle_reward_s2c.parseFrom(s);
			case -1474832921:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_dice_c2s.parseFrom(s);
			case -1637489720:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_dice_grid_s2c.parseFrom(s);
			case -1474817561:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_dice_s2c.parseFrom(s);
			case 262049743:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_dice_time_s2c.parseFrom(s);
			case 1083602513:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_double_c2s.parseFrom(s);
			case 1083617873:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_double_s2c.parseFrom(s);
			case -1113042066:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_info_c2s.parseFrom(s);
			case -1113026706:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_monopoly_info_s2c.parseFrom(s);
			case 1313020877:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_broadcast_s2c.parseFrom(s);
			case 977885266:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_emoji_c2s.parseFrom(s);
			case 977900626:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_emoji_s2c.parseFrom(s);
			case -1566554652:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_enter_c2s.parseFrom(s);
			case -1566539292:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_enter_s2c.parseFrom(s);
			case -344157956:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_exit_c2s.parseFrom(s);
			case -344142596:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_exit_s2c.parseFrom(s);
			case 1674150447:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_move_c2s.parseFrom(s);
			case 1674165807:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_move_s2c.parseFrom(s);
			case -65301750:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_reward_claim_c2s.parseFrom(s);
			case -65286390:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_reward_claim_s2c.parseFrom(s);
			case 324417162:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_reward_info_c2s.parseFrom(s);
			case 324432522:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_move_reward_info_s2c.parseFrom(s);
			case -2112048880:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_new_year_draw_slot_c2s.parseFrom(s);
			case -2112033520:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_new_year_draw_slot_s2c.parseFrom(s);
			case -27957250:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_nine_number_guess_c2s.parseFrom(s);
			case -27941890:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_nine_number_guess_s2c.parseFrom(s);
			case 1037312657:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_nine_number_info_c2s.parseFrom(s);
			case 1037328017:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_nine_number_info_s2c.parseFrom(s);
			case 1094307658:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_nine_number_pre_c2s.parseFrom(s);
			case 1094323018:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_nine_number_pre_s2c.parseFrom(s);
			case -1704818826:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_auto_interception_c2s.parseFrom(s);
			case -1704803466:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_auto_interception_s2c.parseFrom(s);
			case -598454116:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_change_plan_name_c2s.parseFrom(s);
			case -598438756:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_change_plan_name_s2c.parseFrom(s);
			case -1500642986:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_enlighten_c2s.parseFrom(s);
			case 2071157035:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_enlighten_history_c2s.parseFrom(s);
			case 2071172395:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_enlighten_history_s2c.parseFrom(s);
			case -1500627626:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_enlighten_s2c.parseFrom(s);
			case -2021463368:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_info_c2s.parseFrom(s);
			case -2021448008:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_info_s2c.parseFrom(s);
			case -290077515:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_lock_c2s.parseFrom(s);
			case -290062155:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_lock_s2c.parseFrom(s);
			case 1090658388:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_replace_c2s.parseFrom(s);
			case 1090673748:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_replace_s2c.parseFrom(s);
			case 8369387:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_use_plan_c2s.parseFrom(s);
			case 8384747:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_path_use_plan_s2c.parseFrom(s);
			case 1245504747:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_share_game_info_c2s.parseFrom(s);
			case 1245520107:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_share_game_info_s2c.parseFrom(s);
			case -1446255910:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_share_game_result_c2s.parseFrom(s);
			case -1446240550:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_share_game_result_s2c.parseFrom(s);
			case 331117175:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_attr_change_s2c.parseFrom(s);
			case 1824216848:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_enter_stage_c2s.parseFrom(s);
			case 1824232208:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_enter_stage_s2c.parseFrom(s);
			case -811963165:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_finish_stage_c2s.parseFrom(s);
			case -811947805:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_finish_stage_s2c.parseFrom(s);
			case 1648850751:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_info_c2s.parseFrom(s);
			case 1648866111:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_info_s2c.parseFrom(s);
			case 1120902165:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_mix_c2s.parseFrom(s);
			case 1136554946:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_mix_info_c2s.parseFrom(s);
			case 1136570306:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_mix_info_s2c.parseFrom(s);
			case 1120917525:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_mix_s2c.parseFrom(s);
			case 323675924:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_refresh_c2s.parseFrom(s);
			case 323691284:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_refresh_s2c.parseFrom(s);
			case 1273519830:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_skill_choose_c2s.parseFrom(s);
			case 1273535190:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_skill_choose_s2c.parseFrom(s);
			case -225411028:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_skill_refine_c2s.parseFrom(s);
			case -225395668:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_skill_refine_s2c.parseFrom(s);
			case 476228063:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_stage_progress_c2s.parseFrom(s);
			case 476243423:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_stage_progress_s2c.parseFrom(s);
			case 322312101:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_sweep_c2s.parseFrom(s);
			case 322327461:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_sweep_s2c.parseFrom(s);
			case -1515779110:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_talent_upgrade_c2s.parseFrom(s);
			case -1515763750:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_talent_upgrade_s2c.parseFrom(s);
			case 497434681:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_wheel_draw_c2s.parseFrom(s);
			case 497450041:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_slime_wheel_draw_s2c.parseFrom(s);
			case -316239493:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_stamina_refresh_c2s.parseFrom(s);
			case -316224133:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_stamina_refresh_s2c.parseFrom(s);
			case -1019761700:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_star_rain_draw_c2s.parseFrom(s);
			case -1019746340:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_star_rain_draw_s2c.parseFrom(s);
			case -14934458:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_star_rain_info_c2s.parseFrom(s);
			case -14919098:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_star_rain_info_s2c.parseFrom(s);
			case -1271617503:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_star_rain_update_s2c.parseFrom(s);
			case 1600606005:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_sugar_bawl_fill_c2s.parseFrom(s);
			case 1600621365:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_sugar_bawl_fill_s2c.parseFrom(s);
			case 630167978:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_sugar_bawl_get_reward_c2s.parseFrom(s);
			case 630183338:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_sugar_bawl_get_reward_s2c.parseFrom(s);
			case -1787350816:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_sugar_bawl_info_c2s.parseFrom(s);
			case -1787335456:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_sugar_bawl_info_s2c.parseFrom(s);
			case 202448346:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_draw_c2s.parseFrom(s);
			case 202463706:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_draw_s2c.parseFrom(s);
			case 1207275588:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_info_c2s.parseFrom(s);
			case 1207290948:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_info_s2c.parseFrom(s);
			case 842822639:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_refresh_c2s.parseFrom(s);
			case -1634664632:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_replace_c2s.parseFrom(s);
			case -1634649272:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_replace_s2c.parseFrom(s);
			case -1321515638:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_report_c2s.parseFrom(s);
			case -1321500278:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_report_s2c.parseFrom(s);
			case -1561671163:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_reward_c2s.parseFrom(s);
			case -1561655803:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_treasure_hunt_reward_s2c.parseFrom(s);
			case 1185776849:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_handle_invite_c2s.parseFrom(s);
			case 1185792209:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_handle_invite_s2c.parseFrom(s);
			case -1685716185:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_info_c2s.parseFrom(s);
			case -1685700825:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_info_s2c.parseFrom(s);
			case 918909698:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_invite_c2s.parseFrom(s);
			case 1114302709:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_invite_info_c2s.parseFrom(s);
			case 1114318069:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_invite_info_s2c.parseFrom(s);
			case 918925058:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_invite_s2c.parseFrom(s);
			case -989642207:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_search_c2s.parseFrom(s);
			case -989626847:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_valentine_cp_search_s2c.parseFrom(s);
			case -314972461:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_black_hole_c2s.parseFrom(s);
			case -314957101:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_black_hole_s2c.parseFrom(s);
			case -534460631:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_choose_c2s.parseFrom(s);
			case -534445271:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_choose_s2c.parseFrom(s);
			case 1540068273:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_count_reward_c2s.parseFrom(s);
			case 1540083633:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_count_reward_s2c.parseFrom(s);
			case -1718614250:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_draw_c2s.parseFrom(s);
			case -1718598890:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_draw_s2c.parseFrom(s);
			case -713787008:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_info_c2s.parseFrom(s);
			case -713771648:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_draw_info_s2c.parseFrom(s);
			case 1304232454:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_reset_c2s.parseFrom(s);
			case 1304247814:
				return org.gof.demo.worldsrv.msg.MsgAct2.act_yfs_reset_s2c.parseFrom(s);
			case 2050178090:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_info_c2s.parseFrom(s);
			case 2050193450:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_info_s2c.parseFrom(s);
			case 1013781611:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_reward_c2s.parseFrom(s);
			case 1013796971:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_reward_s2c.parseFrom(s);
			case 1777064782:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_wheel_info_c2s.parseFrom(s);
			case 1777080142:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_wheel_info_s2c.parseFrom(s);
			case -409697054:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_wheel_spin_c2s.parseFrom(s);
			case -409681694:
				return org.gof.demo.worldsrv.msg.MsgAd.ad_wheel_spin_s2c.parseFrom(s);
			case -648464855:
				return org.gof.demo.worldsrv.msg.MsgAdventureTitle.adventure_title_info_c2s.parseFrom(s);
			case -648449495:
				return org.gof.demo.worldsrv.msg.MsgAdventureTitle.adventure_title_info_s2c.parseFrom(s);
			case -1171076175:
				return org.gof.demo.worldsrv.msg.MsgAdventureTitle.adventure_title_level_up_c2s.parseFrom(s);
			case -1171060815:
				return org.gof.demo.worldsrv.msg.MsgAdventureTitle.adventure_title_level_up_s2c.parseFrom(s);
			case 503921350:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_array_change_tab_name_c2s.parseFrom(s);
			case 503936710:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_array_change_tab_name_s2c.parseFrom(s);
			case 373642101:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_array_choose_tab_c2s.parseFrom(s);
			case 373657461:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_array_choose_tab_s2c.parseFrom(s);
			case 1853133770:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_battle_rotate_c2s.parseFrom(s);
			case 1853149130:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_battle_rotate_s2c.parseFrom(s);
			case 1278529567:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_develop_rotate_c2s.parseFrom(s);
			case 1278544927:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_develop_rotate_s2c.parseFrom(s);
			case 2145939622:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_draw_c2s.parseFrom(s);
			case -558649495:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_draw_exchang_c2s.parseFrom(s);
			case 813224401:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_draw_info_c2s.parseFrom(s);
			case 813239761:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_draw_info_s2c.parseFrom(s);
			case 2145954982:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_draw_s2c.parseFrom(s);
			case 1005002766:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_goods_exchange_c2s.parseFrom(s);
			case 1005018126:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_goods_exchange_s2c.parseFrom(s);
			case 23508736:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_list_c2s.parseFrom(s);
			case 23524096:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_list_s2c.parseFrom(s);
			case -1922865838:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_list_update_s2c.parseFrom(s);
			case 1894003662:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_star_array_c2s.parseFrom(s);
			case 1894019022:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_star_array_s2c.parseFrom(s);
			case 511245431:
				return org.gof.demo.worldsrv.msg.MsgAngel.angel_upgrade_star_c2s.parseFrom(s);
			case -874442022:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_buy_c2s.parseFrom(s);
			case -874426662:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_buy_s2c.parseFrom(s);
			case -535497206:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_combat_c2s.parseFrom(s);
			case -535481846:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_combat_s2c.parseFrom(s);
			case 300844752:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_cross_sync_c2s.parseFrom(s);
			case 300860112:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_cross_sync_s2c.parseFrom(s);
			case -78433688:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_history_c2s.parseFrom(s);
			case -78418328:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_history_s2c.parseFrom(s);
			case -174971132:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_info_c2s.parseFrom(s);
			case -174955772:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_info_s2c.parseFrom(s);
			case 688382053:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_rank_list_c2s.parseFrom(s);
			case 688397413:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_rank_list_s2c.parseFrom(s);
			case -1817746129:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_refresh_c2s.parseFrom(s);
			case -1817730769:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_refresh_s2c.parseFrom(s);
			case -723975501:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_result_c2s.parseFrom(s);
			case -723960141:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_result_s2c.parseFrom(s);
			case 1805847403:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_role_info_c2s.parseFrom(s);
			case 1805862763:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_role_info_s2c.parseFrom(s);
			case 741480718:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_video_play_c2s.parseFrom(s);
			case 741496078:
				return org.gof.demo.worldsrv.msg.MsgArena.arena_video_play_s2c.parseFrom(s);
			case -1134432758:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_info_c2s.parseFrom(s);
			case -1134417398:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_info_s2c.parseFrom(s);
			case 1062060742:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_levup_c2s.parseFrom(s);
			case 1062076102:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_levup_s2c.parseFrom(s);
			case 1165134639:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_up_skin_c2s.parseFrom(s);
			case 1165149999:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_up_skin_s2c.parseFrom(s);
			case -190041707:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_use_c2s.parseFrom(s);
			case -190026347:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_use_s2c.parseFrom(s);
			case -1311145497:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_use_skill_c2s.parseFrom(s);
			case -1311130137:
				return org.gof.demo.worldsrv.msg.MsgArtifact.artifact_use_skill_s2c.parseFrom(s);
			case -977430452:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_add_s2c.parseFrom(s);
			case -1922605617:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_change_tab_name_c2s.parseFrom(s);
			case -1922590257:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_change_tab_name_s2c.parseFrom(s);
			case 1066162144:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_choose_suit_c2s.parseFrom(s);
			case 1066177504:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_choose_suit_s2c.parseFrom(s);
			case -211964212:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_choose_tab_c2s.parseFrom(s);
			case -211948852:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_choose_tab_s2c.parseFrom(s);
			case 1178594573:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_info_c2s.parseFrom(s);
			case 1178609933:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_info_s2c.parseFrom(s);
			case -1384986870:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_lock_c2s.parseFrom(s);
			case -1384971510:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_lock_s2c.parseFrom(s);
			case 1721969891:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_red_read_c2s.parseFrom(s);
			case 1721985251:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_red_read_s2c.parseFrom(s);
			case 1350271173:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_split_c2s.parseFrom(s);
			case 1350286533:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_split_s2c.parseFrom(s);
			case -1697400213:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_sub_c2s.parseFrom(s);
			case -1697384853:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_sub_s2c.parseFrom(s);
			case 1383272186:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_up_c2s.parseFrom(s);
			case 1383287546:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_up_s2c.parseFrom(s);
			case 270161758:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_wear_c2s.parseFrom(s);
			case 270177118:
				return org.gof.demo.worldsrv.msg.MsgArtifactGem.artifact_gem_wear_s2c.parseFrom(s);
			case -1603924788:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.parseFrom(s);
			case -1603909428:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.parseFrom(s);
			case -361162307:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.parseFrom(s);
			case -361146947:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.parseFrom(s);
			case -101687757:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.parseFrom(s);
			case -101672397:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.parseFrom(s);
			case -2085109845:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.parseFrom(s);
			case -2085094485:
				return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.parseFrom(s);
			case 232498852:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_add_player_c2s.parseFrom(s);
			case 232514212:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_add_player_s2c.parseFrom(s);
			case 1114603584:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_end_c2s.parseFrom(s);
			case 1114618944:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_end_s2c.parseFrom(s);
			case -1353759853:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_info_c2s.parseFrom(s);
			case -1353744493:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_info_s2c.parseFrom(s);
			case 1918025799:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_start_c2s.parseFrom(s);
			case 1918041159:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_start_s2c.parseFrom(s);
			case 607351342:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_update_c2s.parseFrom(s);
			case 607366702:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_gve_update_s2c.parseFrom(s);
			case 132454521:
				return org.gof.demo.worldsrv.msg.MsgBattle.battle_result_c2s.parseFrom(s);
			case -820641417:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.add_snapshot_c2s.parseFrom(s);
			case -820626057:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.add_snapshot_s2c.parseFrom(s);
			case -1817851254:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.battle_heartbeat_c2s.parseFrom(s);
			case -1817835894:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.battle_heartbeat_s2c.parseFrom(s);
			case -1031119714:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.battle_stop_c2s.parseFrom(s);
			case -1031104354:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.battle_stop_s2c.parseFrom(s);
			case -483034471:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.snapshot_c2s.parseFrom(s);
			case -483019111:
				return org.gof.demo.worldsrv.msg.MsgBattleCheck.snapshot_s2c.parseFrom(s);
			case 1006760174:
				return org.gof.demo.worldsrv.msg.MsgCaptcha.captcha_answer_c2s.parseFrom(s);
			case 1006775534:
				return org.gof.demo.worldsrv.msg.MsgCaptcha.captcha_answer_s2c.parseFrom(s);
			case 69867861:
				return org.gof.demo.worldsrv.msg.MsgCaptcha.captcha_refresh_c2s.parseFrom(s);
			case 69883221:
				return org.gof.demo.worldsrv.msg.MsgCaptcha.captcha_refresh_s2c.parseFrom(s);
			case -320947791:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_combat_c2s.parseFrom(s);
			case -320932431:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_combat_s2c.parseFrom(s);
			case 1778245070:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_effect_c2s.parseFrom(s);
			case 1778260430:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_effect_s2c.parseFrom(s);
			case 53103401:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_free_c2s.parseFrom(s);
			case 53118761:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_free_s2c.parseFrom(s);
			case 1769384043:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_info_c2s.parseFrom(s);
			case 1769399403:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_info_s2c.parseFrom(s);
			case -450647588:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_look_c2s.parseFrom(s);
			case -450632228:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_look_s2c.parseFrom(s);
			case 538318440:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_refresh_c2s.parseFrom(s);
			case 538333800:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_refresh_s2c.parseFrom(s);
			case -1188971269:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_rename_c2s.parseFrom(s);
			case -1188955909:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_rename_s2c.parseFrom(s);
			case -509426086:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_result_c2s.parseFrom(s);
			case -509410726:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_result_s2c.parseFrom(s);
			case 1753642092:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_reward_c2s.parseFrom(s);
			case 1224291331:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_reward_get_c2s.parseFrom(s);
			case 1224306691:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_reward_get_s2c.parseFrom(s);
			case 1753657452:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_reward_s2c.parseFrom(s);
			case -648042907:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_search_c2s.parseFrom(s);
			case -648027547:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_search_s2c.parseFrom(s);
			case -881487352:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_video_c2s.parseFrom(s);
			case 1905504565:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_video_play_c2s.parseFrom(s);
			case 1905519925:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_video_play_s2c.parseFrom(s);
			case -881471992:
				return org.gof.demo.worldsrv.msg.MsgCaptureSlave.capture_slave_video_s2c.parseFrom(s);
			case -1798539162:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_car_info_c2s.parseFrom(s);
			case -1798523802:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_car_info_s2c.parseFrom(s);
			case 1764531347:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_car_up_c2s.parseFrom(s);
			case 1764546707:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_car_up_s2c.parseFrom(s);
			case -2116818105:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_collect_c2s.parseFrom(s);
			case -2116802745:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_collect_s2c.parseFrom(s);
			case -95489631:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_combat_c2s.parseFrom(s);
			case -95474271:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_combat_s2c.parseFrom(s);
			case 676899249:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_history_c2s.parseFrom(s);
			case 676914609:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_history_s2c.parseFrom(s);
			case 1081351259:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_info_c2s.parseFrom(s);
			case 1081366619:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_info_s2c.parseFrom(s);
			case -1138680372:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_look_c2s.parseFrom(s);
			case -1138665012:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_look_s2c.parseFrom(s);
			case -187661010:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_mount_id_c2s.parseFrom(s);
			case -187645650:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_mount_id_s2c.parseFrom(s);
			case -1246795243:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_help_c2s.parseFrom(s);
			case -1246779883:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_help_s2c.parseFrom(s);
			case 873214136:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_start_c2s.parseFrom(s);
			case 873229496:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_start_s2c.parseFrom(s);
			case 15589816:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_stop_all_c2s.parseFrom(s);
			case 15605176:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_stop_all_s2c.parseFrom(s);
			case 1257790038:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_stop_c2s.parseFrom(s);
			case 1257805398:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_parking_stop_s2c.parseFrom(s);
			case -366737812:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_protect_c2s.parseFrom(s);
			case -366722452:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_protect_s2c.parseFrom(s);
			case 35872067:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_read_c2s.parseFrom(s);
			case 35887427:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_read_s2c.parseFrom(s);
			case -963513109:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_rename_c2s.parseFrom(s);
			case -963497749:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_rename_s2c.parseFrom(s);
			case -2075711519:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_report_c2s.parseFrom(s);
			case -2075696159:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_report_s2c.parseFrom(s);
			case -283967926:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_result_c2s.parseFrom(s);
			case -283952566:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_result_s2c.parseFrom(s);
			case -422584747:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_search_c2s.parseFrom(s);
			case -422569387:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_search_s2c.parseFrom(s);
			case -217589446:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_skin_up_c2s.parseFrom(s);
			case -217574086:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_skin_up_s2c.parseFrom(s);
			case 1936016370:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_skin_use_c2s.parseFrom(s);
			case 1936031730:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_skin_use_s2c.parseFrom(s);
			case 549341135:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_space_update_c2s.parseFrom(s);
			case 549356495:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_space_update_s2c.parseFrom(s);
			case 1531343141:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_video_play_c2s.parseFrom(s);
			case 1531358501:
				return org.gof.demo.worldsrv.msg.MsgCarPark.car_park_video_play_s2c.parseFrom(s);
			case -1702543688:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_battle_queue_c2s.parseFrom(s);
			case -1702528328:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_battle_queue_s2c.parseFrom(s);
			case -1154922596:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_info_c2s.parseFrom(s);
			case -1154907236:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_info_s2c.parseFrom(s);
			case -713192082:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_look_fighting_c2s.parseFrom(s);
			case -713176722:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_look_fighting_s2c.parseFrom(s);
			case -929001566:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_merge_time_c2s.parseFrom(s);
			case -928986206:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_merge_time_s2c.parseFrom(s);
			case -394476907:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_next_time_c2s.parseFrom(s);
			case -394461547:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_next_time_s2c.parseFrom(s);
			case 1369251479:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_parking_start_c2s.parseFrom(s);
			case 1369266839:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_parking_start_s2c.parseFrom(s);
			case 1689433239:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_parking_stop_c2s.parseFrom(s);
			case 1689448599:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_parking_stop_s2c.parseFrom(s);
			case 1731948548:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_preview_c2s.parseFrom(s);
			case 1731963908:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_preview_s2c.parseFrom(s);
			case 554275526:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_queue_join_c2s.parseFrom(s);
			case 735655838:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_queue_stick_c2s.parseFrom(s);
			case -1076805606:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_rank_c2s.parseFrom(s);
			case -1076790246:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_rank_s2c.parseFrom(s);
			case 1516049669:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_report_list_c2s.parseFrom(s);
			case 1516065029:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_report_list_s2c.parseFrom(s);
			case 1803588278:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_role_state_c2s.parseFrom(s);
			case 1803603638:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_role_state_s2c.parseFrom(s);
			case -1214538887:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_settle_s2c.parseFrom(s);
			case 1420060582:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_video_play_c2s.parseFrom(s);
			case 1420075942:
				return org.gof.demo.worldsrv.msg.MsgCarPark.cross_car_park_video_play_s2c.parseFrom(s);
			case 466937571:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.parseFrom(s);
			case 466952931:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.parseFrom(s);
			case 1569486439:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.parseFrom(s);
			case 1569501799:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.parseFrom(s);
			case 1395325911:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.parseFrom(s);
			case 1395341271:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.parseFrom(s);
			case -1707120284:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.parseFrom(s);
			case -1823061782:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.parseFrom(s);
			case -1823046422:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.parseFrom(s);
			case -1369649632:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.parseFrom(s);
			case -1369634272:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.parseFrom(s);
			case -262983844:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.parseFrom(s);
			case -262968484:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.parseFrom(s);
			case 2131308600:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.parseFrom(s);
			case 2131323960:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.parseFrom(s);
			case -1697162664:
				return org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.parseFrom(s);
			case -1549188163:
				return org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.parseFrom(s);
			case -1549172803:
				return org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.parseFrom(s);
			case -1690103992:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.parseFrom(s);
			case -1272563210:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.parseFrom(s);
			case -1272547850:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.parseFrom(s);
			case 939278154:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.parseFrom(s);
			case 939293514:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.parseFrom(s);
			case 771786338:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.parseFrom(s);
			case 1772368509:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.parseFrom(s);
			case 1772383869:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.parseFrom(s);
			case 771801698:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.parseFrom(s);
			case -453332907:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.parseFrom(s);
			case -453317547:
				return org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.parseFrom(s);
			case 827231164:
				return org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.parseFrom(s);
			case -911162803:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.parseFrom(s);
			case -911147443:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.parseFrom(s);
			case -836175581:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.parseFrom(s);
			case -836160221:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.parseFrom(s);
			case -1361608521:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.parseFrom(s);
			case -1361593161:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.parseFrom(s);
			case -460926618:
				return org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.parseFrom(s);
			case -812363588:
				return org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.parseFrom(s);
			case -812348228:
				return org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.parseFrom(s);
			case -492837878:
				return org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.parseFrom(s);
			case -492822518:
				return org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.parseFrom(s);
			case 1576156939:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.parseFrom(s);
			case 1576172299:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.parseFrom(s);
			case 958335367:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.parseFrom(s);
			case 958350727:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.parseFrom(s);
			case 2021637189:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.parseFrom(s);
			case 2021652549:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.parseFrom(s);
			case -1777203818:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.parseFrom(s);
			case -576677932:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.parseFrom(s);
			case -576662572:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.parseFrom(s);
			case 1387678644:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.parseFrom(s);
			case 1387694004:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.parseFrom(s);
			case -223184688:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.parseFrom(s);
			case -223169328:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.parseFrom(s);
			case -1147606415:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.parseFrom(s);
			case 2018572687:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.parseFrom(s);
			case 2018588047:
				return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.parseFrom(s);
			case -268797814:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_battle_report_s2c.parseFrom(s);
			case 1753082544:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_choose_job_c2s.parseFrom(s);
			case 1753097904:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_choose_job_s2c.parseFrom(s);
			case 345745152:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_get_idle_reward_c2s.parseFrom(s);
			case 345760512:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_get_idle_reward_s2c.parseFrom(s);
			case 1259285217:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_goods_show_s2c.parseFrom(s);
			case -548044055:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_idle_reward_c2s.parseFrom(s);
			case -548028695:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_idle_reward_s2c.parseFrom(s);
			case -729080375:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_info_c2s.parseFrom(s);
			case -729065015:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_info_s2c.parseFrom(s);
			case 379032603:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_is_holy_open_c2s.parseFrom(s);
			case 379047963:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_is_holy_open_s2c.parseFrom(s);
			case 2145084878:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_kill_list_c2s.parseFrom(s);
			case 2145100238:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_kill_list_s2c.parseFrom(s);
			case 408579142:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_reconnect_c2s.parseFrom(s);
			case -1501220496:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_revive_c2s.parseFrom(s);
			case -1501205136:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_revive_s2c.parseFrom(s);
			case -866228012:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_enter_c2s.parseFrom(s);
			case -99109988:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_info_c2s.parseFrom(s);
			case -99094628:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_info_s2c.parseFrom(s);
			case 1653057843:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_leave_c2s.parseFrom(s);
			case -1919963478:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_select_c2s.parseFrom(s);
			case -1919948118:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_select_s2c.parseFrom(s);
			case -1650715965:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_speed_c2s.parseFrom(s);
			case -1650700605:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_speed_s2c.parseFrom(s);
			case 1536013273:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_transfer_c2s.parseFrom(s);
			case 264776018:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_transfer_close_c2s.parseFrom(s);
			case -49216373:
				return org.gof.demo.worldsrv.msg.MsgCrossWar.cross_war_scene_transfer_slide_c2s.parseFrom(s);
			case -240393154:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_create_chat_c2s.parseFrom(s);
			case -240377794:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_create_chat_s2c.parseFrom(s);
			case 1384191285:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_info_c2s.parseFrom(s);
			case 1384206645:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_info_s2c.parseFrom(s);
			case -843283745:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_input_state_c2s.parseFrom(s);
			case -843268385:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_input_state_s2c.parseFrom(s);
			case -1922794491:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_kf_list_c2s.parseFrom(s);
			case -1922779131:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_kf_list_s2c.parseFrom(s);
			case 256465546:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_kf_register_s2c.parseFrom(s);
			case 1728881329:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_kf_send_msg_s2c.parseFrom(s);
			case -1147186397:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_kf_unregister_s2c.parseFrom(s);
			case 101380520:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_offline_msg_c2s.parseFrom(s);
			case 101395880:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_offline_msg_s2c.parseFrom(s);
			case 679953361:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_send_msg_c2s.parseFrom(s);
			case 679968721:
				return org.gof.demo.worldsrv.msg.MsgCustomerService.customer_service_send_msg_s2c.parseFrom(s);
			case -1350018546:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_change_optional_reward_c2s.parseFrom(s);
			case -1350003186:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_change_optional_reward_s2c.parseFrom(s);
			case -667145011:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_abandon_teammate_c2s.parseFrom(s);
			case -667129651:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_abandon_teammate_s2c.parseFrom(s);
			case 664516957:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_agree_invite_c2s.parseFrom(s);
			case 664532317:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_agree_invite_s2c.parseFrom(s);
			case 90840783:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_cancel_help_c2s.parseFrom(s);
			case 90856143:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_cancel_help_s2c.parseFrom(s);
			case 1870805490:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_change_invite_setting_c2s.parseFrom(s);
			case 1870820850:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_change_invite_setting_s2c.parseFrom(s);
			case 53172561:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_change_pos_info_c2s.parseFrom(s);
			case 53187921:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_change_pos_info_s2c.parseFrom(s);
			case -741486581:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_change_strategy_c2s.parseFrom(s);
			case -741471221:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_change_strategy_s2c.parseFrom(s);
			case -2146496780:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_help_manage_info_c2s.parseFrom(s);
			case -2146481420:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_help_manage_info_s2c.parseFrom(s);
			case -164671761:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_invite_help_setting_info_c2s.parseFrom(s);
			case -164656401:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_invite_help_setting_info_s2c.parseFrom(s);
			case 1515657331:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_level_info_c2s.parseFrom(s);
			case 1515672691:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_level_info_s2c.parseFrom(s);
			case -1533893973:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_teammate_info_c2s.parseFrom(s);
			case -1533878613:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_teammate_info_s2c.parseFrom(s);
			case 1565120564:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_teammate_skill_delay_time_c2s.parseFrom(s);
			case 1565135924:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_teammate_skill_delay_time_s2c.parseFrom(s);
			case -933780435:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_through_level_info_c2s.parseFrom(s);
			case -933765075:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_get_through_level_info_s2c.parseFrom(s);
			case 213904483:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_info_update_s2c.parseFrom(s);
			case -1603533544:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_is_in_invite_list_c2s.parseFrom(s);
			case -1603518184:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_is_in_invite_list_s2c.parseFrom(s);
			case 1725726333:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_refuse_invite_c2s.parseFrom(s);
			case 1725741693:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_refuse_invite_s2c.parseFrom(s);
			case -460573306:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_restart_chapter_c2s.parseFrom(s);
			case -460557946:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_restart_chapter_s2c.parseFrom(s);
			case -405510595:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_restart_level_c2s.parseFrom(s);
			case -405495235:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_restart_level_s2c.parseFrom(s);
			case 1130801216:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_set_teammate_skill_delay_time_c2s.parseFrom(s);
			case 1130816576:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_set_teammate_skill_delay_time_s2c.parseFrom(s);
			case -420118123:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_single_teammate_info_c2s.parseFrom(s);
			case -420102763:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_single_teammate_info_s2c.parseFrom(s);
			case 1510809851:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_use_teammate_c2s.parseFrom(s);
			case 1510825211:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_use_teammate_s2c.parseFrom(s);
			case 1864072387:
				return org.gof.demo.worldsrv.msg.MsgDoubleChapter.double_chapter_week_reward_s2c.parseFrom(s);
			case 633444140:
				return org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.parseFrom(s);
			case 633459500:
				return org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.parseFrom(s);
			case 819626442:
				return org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.parseFrom(s);
			case 819641802:
				return org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.parseFrom(s);
			case -789598311:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_battle_more_start_c2s.parseFrom(s);
			case -789582951:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_battle_more_start_s2c.parseFrom(s);
			case 37850174:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_battle_result_c2s.parseFrom(s);
			case 37865534:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_battle_result_s2c.parseFrom(s);
			case -1000938485:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_battle_start_c2s.parseFrom(s);
			case -1000923125:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_battle_start_s2c.parseFrom(s);
			case 426173604:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_combat_c2s.parseFrom(s);
			case 426188964:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_combat_s2c.parseFrom(s);
			case -462191801:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_dc_entrance_info_c2s.parseFrom(s);
			case -462176441:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_dc_entrance_info_s2c.parseFrom(s);
			case -1996440088:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_dc_info_c2s.parseFrom(s);
			case -1996424728:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_dc_info_s2c.parseFrom(s);
			case -633179326:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_fate_daily_reward_c2s.parseFrom(s);
			case -633163966:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_fate_daily_reward_s2c.parseFrom(s);
			case -1512358307:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_idol_main_info_c2s.parseFrom(s);
			case -1512342947:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_idol_main_info_s2c.parseFrom(s);
			case -1941193849:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_hard_buff_c2s.parseFrom(s);
			case -1941178489:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_hard_buff_s2c.parseFrom(s);
			case -240702878:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_hard_info_c2s.parseFrom(s);
			case -240687518:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_hard_info_s2c.parseFrom(s);
			case -1834954952:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_solo_get_reward_c2s.parseFrom(s);
			case -1630852882:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_solo_info_c2s.parseFrom(s);
			case -1630837522:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_solo_info_s2c.parseFrom(s);
			case 929734101:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_league_solo_update_box_s2c.parseFrom(s);
			case -981678066:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_list_c2s.parseFrom(s);
			case -981662706:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_list_s2c.parseFrom(s);
			case 1651485144:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_mount_battle_result_c2s.parseFrom(s);
			case 1651500504:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_mount_battle_result_s2c.parseFrom(s);
			case 237695309:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_result_c2s.parseFrom(s);
			case 237710669:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_result_s2c.parseFrom(s);
			case -1271586532:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_start_c2s.parseFrom(s);
			case -1271571172:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_start_s2c.parseFrom(s);
			case -1458948442:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_sweep_c2s.parseFrom(s);
			case -1458933082:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_sweep_s2c.parseFrom(s);
			case 1291319466:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_type_c2s.parseFrom(s);
			case 1291334826:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_type_s2c.parseFrom(s);
			case 513632249:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_update_s2c.parseFrom(s);
			case -1358407727:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_wing_quick_pass_c2s.parseFrom(s);
			case -1902667573:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_wing_reset_c2s.parseFrom(s);
			case 1677587279:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_world_boss_change_c2s.parseFrom(s);
			case 1677602639:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_world_boss_change_s2c.parseFrom(s);
			case 30268365:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_world_boss_info_c2s.parseFrom(s);
			case 30283725:
				return org.gof.demo.worldsrv.msg.MsgDungeon.dungeon_world_boss_info_s2c.parseFrom(s);
			case 1851627098:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_book_list_c2s.parseFrom(s);
			case 1851642458:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_book_list_s2c.parseFrom(s);
			case 310629189:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_book_update_s2c.parseFrom(s);
			case -1362353114:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_info_c2s.parseFrom(s);
			case -1362337754:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_info_s2c.parseFrom(s);
			case -207804478:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_lv_c2s.parseFrom(s);
			case 781035812:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_open_all_c2s.parseFrom(s);
			case 781051172:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_open_all_s2c.parseFrom(s);
			case -2044938302:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_open_c2s.parseFrom(s);
			case -2044922942:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_open_s2c.parseFrom(s);
			case 603301237:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_skin_c2s.parseFrom(s);
			case 603316597:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_skin_s2c.parseFrom(s);
			case -1088426440:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_skin_unlock_c2s.parseFrom(s);
			case -1088411080:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_box_skin_unlock_s2c.parseFrom(s);
			case 2135170694:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_change_box_skin_c2s.parseFrom(s);
			case 2135186054:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_change_box_skin_s2c.parseFrom(s);
			case -1798147596:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_change_s2c.parseFrom(s);
			case -723476982:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_change_tab_name_c2s.parseFrom(s);
			case -723461622:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_change_tab_name_s2c.parseFrom(s);
			case 451278257:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_choose_tab_c2s.parseFrom(s);
			case -95751384:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_figure_c2s.parseFrom(s);
			case -590912385:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_figure_list_c2s.parseFrom(s);
			case -590897025:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_figure_list_s2c.parseFrom(s);
			case -95736024:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_figure_s2c.parseFrom(s);
			case 647156286:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_filter_attr_c2s.parseFrom(s);
			case 271103611:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_filter_info_c2s.parseFrom(s);
			case 271118971:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_filter_info_s2c.parseFrom(s);
			case 799835058:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_info_c2s.parseFrom(s);
			case 799850418:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_info_s2c.parseFrom(s);
			case -1322200768:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_select_c2s.parseFrom(s);
			case -1322185408:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_select_s2c.parseFrom(s);
			case 276600314:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_shop_c2s.parseFrom(s);
			case 276615674:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_shop_s2c.parseFrom(s);
			case 376980892:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_tab_info_c2s.parseFrom(s);
			case 376996252:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_tab_info_s2c.parseFrom(s);
			case -108597757:
				return org.gof.demo.worldsrv.msg.MsgEquip.equip_wear_c2s.parseFrom(s);
			case -1810000894:
				return org.gof.demo.worldsrv.msg.MsgError.error_info_s2c.parseFrom(s);
			case -1151602449:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_dismantle_c2s.parseFrom(s);
			case -1151587089:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_dismantle_s2c.parseFrom(s);
			case -1453833376:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_fusion_c2s.parseFrom(s);
			case -410069184:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_fusion_choose_c2s.parseFrom(s);
			case -410053824:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_fusion_choose_s2c.parseFrom(s);
			case 729916247:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_fusion_info_c2s.parseFrom(s);
			case 729931607:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_fusion_info_s2c.parseFrom(s);
			case -1453818016:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_fusion_s2c.parseFrom(s);
			case 86370506:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_info_c2s.parseFrom(s);
			case 86385866:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_info_s2c.parseFrom(s);
			case -964297363:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_inlay_c2s.parseFrom(s);
			case -964282003:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_inlay_s2c.parseFrom(s);
			case -181189166:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_level_up_c2s.parseFrom(s);
			case -181173806:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_level_up_s2c.parseFrom(s);
			case 1297265904:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pos_update_s2c.parseFrom(s);
			case -1477722154:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pray_c2s.parseFrom(s);
			case -1290305375:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pray_info_c2s.parseFrom(s);
			case -1290290015:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pray_info_s2c.parseFrom(s);
			case -1477706794:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pray_s2c.parseFrom(s);
			case -2057413281:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pray_set_auto_c2s.parseFrom(s);
			case -2057397921:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_pray_set_auto_s2c.parseFrom(s);
			case 108724768:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_red_read_c2s.parseFrom(s);
			case 108740128:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_red_read_s2c.parseFrom(s);
			case -824452003:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_reset_c2s.parseFrom(s);
			case -824436643:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_reset_s2c.parseFrom(s);
			case 399636711:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_show_choose_c2s.parseFrom(s);
			case 399652071:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_show_choose_s2c.parseFrom(s);
			case 1593172389:
				return org.gof.demo.worldsrv.msg.MsgFate.fate_update_s2c.parseFrom(s);
			case -1888576001:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_egg_incubate_c2s.parseFrom(s);
			case -1888560641:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_egg_incubate_s2c.parseFrom(s);
			case -2111770394:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_egg_info_c2s.parseFrom(s);
			case -2111755034:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_egg_info_s2c.parseFrom(s);
			case -1991290175:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_egg_update_s2c.parseFrom(s);
			case -1013155433:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_base_info_c2s.parseFrom(s);
			case -1013140073:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_base_info_s2c.parseFrom(s);
			case 358003769:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_change_base_name_c2s.parseFrom(s);
			case 358019129:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_change_base_name_s2c.parseFrom(s);
			case -1858886895:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_get_c2s.parseFrom(s);
			case -1858871535:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_get_s2c.parseFrom(s);
			case -335284715:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_kick_c2s.parseFrom(s);
			case -126550740:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_partner_shelves_c2s.parseFrom(s);
			case -126535380:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_partner_shelves_s2c.parseFrom(s);
			case -2077082979:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_pet_info_c2s.parseFrom(s);
			case -2077067619:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_pet_info_s2c.parseFrom(s);
			case 187122335:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_resp_c2s.parseFrom(s);
			case 187137695:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_resp_s2c.parseFrom(s);
			case -1567131811:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_save_setting_c2s.parseFrom(s);
			case -1567116451:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_save_setting_s2c.parseFrom(s);
			case -1240894031:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_set_shelves_info_c2s.parseFrom(s);
			case -1240878671:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_set_shelves_info_s2c.parseFrom(s);
			case -521567116:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_shelves_info_c2s.parseFrom(s);
			case -521551756:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_shelves_info_s2c.parseFrom(s);
			case 1352103069:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_start_c2s.parseFrom(s);
			case 1352118429:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_hybrid_start_s2c.parseFrom(s);
			case -319687602:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_advance_c2s.parseFrom(s);
			case -319672242:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_advance_s2c.parseFrom(s);
			case 1427642428:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_collection_c2s.parseFrom(s);
			case 1427657788:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_collection_s2c.parseFrom(s);
			case -406836580:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_fight_c2s.parseFrom(s);
			case -406821220:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_fight_s2c.parseFrom(s);
			case 347366668:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_info_c2s.parseFrom(s);
			case 347382028:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_info_s2c.parseFrom(s);
			case 1690685716:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_level_up_c2s.parseFrom(s);
			case 1690701076:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_level_up_s2c.parseFrom(s);
			case 2078752521:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_lock_c2s.parseFrom(s);
			case 2078767881:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_lock_s2c.parseFrom(s);
			case -1695348966:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_reborn_c2s.parseFrom(s);
			case -1695333606:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_reborn_s2c.parseFrom(s);
			case -1948068516:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_rename_c2s.parseFrom(s);
			case -1948053156:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_rename_s2c.parseFrom(s);
			case -1323505573:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_reset_c2s.parseFrom(s);
			case -1323490213:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_reset_s2c.parseFrom(s);
			case 1821247672:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_resolve_c2s.parseFrom(s);
			case -136811520:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_resolve_reward_c2s.parseFrom(s);
			case -136796160:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_resolve_reward_s2c.parseFrom(s);
			case 1821263032:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_resolve_s2c.parseFrom(s);
			case 1485280432:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_star_c2s.parseFrom(s);
			case 1485295792:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_star_s2c.parseFrom(s);
			case -1504518744:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_upgrade_quality_c2s.parseFrom(s);
			case -1504503384:
				return org.gof.demo.worldsrv.msg.MsgFly.fly_pet_upgrade_quality_s2c.parseFrom(s);
			case -1341679633:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_claim_gift_c2s.parseFrom(s);
			case -1341664273:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_claim_gift_s2c.parseFrom(s);
			case 1665906120:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_deal_c2s.parseFrom(s);
			case 1665921480:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_deal_s2c.parseFrom(s);
			case 1025983661:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_favor_change_s2c.parseFrom(s);
			case -696858692:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_favor_process_c2s.parseFrom(s);
			case -696843332:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_favor_process_s2c.parseFrom(s);
			case 1370969171:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_gift_num_c2s.parseFrom(s);
			case 1370984531:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_gift_num_s2c.parseFrom(s);
			case -201803849:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_give_and_claim_gift_c2s.parseFrom(s);
			case -201788489:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_give_and_claim_gift_s2c.parseFrom(s);
			case -2039308180:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_give_gift_c2s.parseFrom(s);
			case -2039292820:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_give_gift_s2c.parseFrom(s);
			case 587385936:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_id_list_s2c.parseFrom(s);
			case -1793720070:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_list_c2s.parseFrom(s);
			case -1793704710:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_list_s2c.parseFrom(s);
			case 215173528:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_online_list_c2s.parseFrom(s);
			case 215188888:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_online_list_s2c.parseFrom(s);
			case 730220838:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_refresh_recommend_c2s.parseFrom(s);
			case 730236198:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_refresh_recommend_s2c.parseFrom(s);
			case -1318048567:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_req_deal_c2s.parseFrom(s);
			case 1410760516:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_search_c2s.parseFrom(s);
			case 1410775876:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_search_s2c.parseFrom(s);
			case 1825314277:
				return org.gof.demo.worldsrv.msg.MsgFriend.friend_update_s2c.parseFrom(s);
			case 834059990:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_get_reward_all_c2s.parseFrom(s);
			case 834075350:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_get_reward_all_s2c.parseFrom(s);
			case -1215793548:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_get_reward_c2s.parseFrom(s);
			case -1215778188:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_get_reward_s2c.parseFrom(s);
			case -681579222:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_info_c2s.parseFrom(s);
			case -681563862:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_info_s2c.parseFrom(s);
			case 486129946:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_list_c2s.parseFrom(s);
			case 486145306:
				return org.gof.demo.worldsrv.msg.MsgFund.fund_list_s2c.parseFrom(s);
			case -1058060235:
				return org.gof.demo.worldsrv.msg.MsgGainBuff.gain_buff_info_c2s.parseFrom(s);
			case -1058044875:
				return org.gof.demo.worldsrv.msg.MsgGainBuff.gain_buff_info_s2c.parseFrom(s);
			case 1306858064:
				return org.gof.demo.worldsrv.msg.MsgGainBuff.gain_buff_update_s2c.parseFrom(s);
			case 1487548017:
				return org.gof.demo.worldsrv.msg.MsgGameCentre.game_centre_reward_info_c2s.parseFrom(s);
			case 1487563377:
				return org.gof.demo.worldsrv.msg.MsgGameCentre.game_centre_reward_info_s2c.parseFrom(s);
			case 1353728026:
				return org.gof.demo.worldsrv.msg.MsgGameCentre.game_centre_take_reward_c2s.parseFrom(s);
			case 1353743386:
				return org.gof.demo.worldsrv.msg.MsgGameCentre.game_centre_take_reward_s2c.parseFrom(s);
			case 997377777:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_create_room_c2s.parseFrom(s);
			case 997393137:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_create_room_s2c.parseFrom(s);
			case -916846023:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_enter_room_c2s.parseFrom(s);
			case -916830663:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_enter_room_s2c.parseFrom(s);
			case 973786245:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_info_c2s.parseFrom(s);
			case 973801605:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_info_s2c.parseFrom(s);
			case 1195203808:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_invite_c2s.parseFrom(s);
			case 1195219168:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_invite_s2c.parseFrom(s);
			case -67693027:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_kick_c2s.parseFrom(s);
			case -67677667:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_kick_s2c.parseFrom(s);
			case -2052812438:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_question_complaint_c2s.parseFrom(s);
			case 249038279:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_question_select_c2s.parseFrom(s);
			case -1295102359:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_reconnect_c2s.parseFrom(s);
			case -1295086999:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_reconnect_s2c.parseFrom(s);
			case -1274366300:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_score_s2c.parseFrom(s);
			case 1322012977:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_seq_c2s.parseFrom(s);
			case -1412880824:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_seq_get_c2s.parseFrom(s);
			case -1412865464:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_seq_get_s2c.parseFrom(s);
			case 1322028337:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_seq_s2c.parseFrom(s);
			case -1194253021:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_paint_state_s2c.parseFrom(s);
			case 2096423510:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_ready_c2s.parseFrom(s);
			case 957615109:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_role_leave_c2s.parseFrom(s);
			case 957630469:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_role_leave_s2c.parseFrom(s);
			case 70224418:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_settle_s2c.parseFrom(s);
			case -1704600821:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_desc_c2s.parseFrom(s);
			case -1704585461:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_desc_s2c.parseFrom(s);
			case 621060807:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_guess_c2s.parseFrom(s);
			case 1841490442:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_init_c2s.parseFrom(s);
			case 1841505802:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_init_s2c.parseFrom(s);
			case -734731261:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_update_s2c.parseFrom(s);
			case -83207452:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_vote_c2s.parseFrom(s);
			case -83192092:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_spy_vote_s2c.parseFrom(s);
			case 1057510805:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_start_c2s.parseFrom(s);
			case 1057526165:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_start_s2c.parseFrom(s);
			case -327522533:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_system_chat_s2c.parseFrom(s);
			case -1019653261:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_type_ext_c2s.parseFrom(s);
			case -1019637901:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_type_ext_s2c.parseFrom(s);
			case 460586054:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_type_info_c2s.parseFrom(s);
			case 460601414:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_type_info_s2c.parseFrom(s);
			case -720200737:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_update_role_s2c.parseFrom(s);
			case -626925116:
				return org.gof.demo.worldsrv.msg.MsgGameRoom.game_room_update_room_s2c.parseFrom(s);
			case -815553256:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.parseFrom(s);
			case 728024632:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.parseFrom(s);
			case -1761431384:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.parseFrom(s);
			case 878110134:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.parseFrom(s);
			case 878125494:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.parseFrom(s);
			case 1806506638:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.parseFrom(s);
			case 1806521998:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.parseFrom(s);
			case -2038112410:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.parseFrom(s);
			case -2038097050:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.parseFrom(s);
			case 1740100149:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.parseFrom(s);
			case 334996665:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.parseFrom(s);
			case 335012025:
				return org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.parseFrom(s);
			case -1011743014:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_apply_c2s.parseFrom(s);
			case 2006110221:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_apply_list_c2s.parseFrom(s);
			case 2006125581:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_apply_list_s2c.parseFrom(s);
			case -1011727654:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_apply_s2c.parseFrom(s);
			case 621042041:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_approve_c2s.parseFrom(s);
			case 621057401:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_approve_s2c.parseFrom(s);
			case -1611029427:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_broadcast_s2c.parseFrom(s);
			case 1106185828:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_enter_c2s.parseFrom(s);
			case 1106201188:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_enter_s2c.parseFrom(s);
			case 988985468:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_exit_c2s.parseFrom(s);
			case 989000828:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_exit_s2c.parseFrom(s);
			case -1287673425:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_move_c2s.parseFrom(s);
			case -1287658065:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_area_move_s2c.parseFrom(s);
			case 566173657:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_change_career_c2s.parseFrom(s);
			case 566189017:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_change_career_s2c.parseFrom(s);
			case -18592710:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_create_c2s.parseFrom(s);
			case -18577350:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_create_s2c.parseFrom(s);
			case 1501684662:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_dice_point_c2s.parseFrom(s);
			case 1501700022:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_dice_point_s2c.parseFrom(s);
			case 2102827400:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_dice_start_c2s.parseFrom(s);
			case 2102842760:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_dice_start_s2c.parseFrom(s);
			case -36494609:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_dissolve_c2s.parseFrom(s);
			case -36479249:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_dissolve_s2c.parseFrom(s);
			case -582286291:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_donate_c2s.parseFrom(s);
			case -582270931:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_donate_s2c.parseFrom(s);
			case -1787416071:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_ask_c2s.parseFrom(s);
			case -1787400711:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_ask_s2c.parseFrom(s);
			case -1863519681:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_c2s.parseFrom(s);
			case -1552948070:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_change_s2c.parseFrom(s);
			case 1211262936:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_info_c2s.parseFrom(s);
			case 1211278296:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_info_s2c.parseFrom(s);
			case -1863504321:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_s2c.parseFrom(s);
			case 826962908:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_status_c2s.parseFrom(s);
			case 826978268:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_help_status_s2c.parseFrom(s);
			case -895809236:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_info_c2s.parseFrom(s);
			case -895793876:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_info_s2c.parseFrom(s);
			case -1128101466:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_info_update_s2c.parseFrom(s);
			case 1819468712:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_join_c2s.parseFrom(s);
			case 1819484072:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_join_s2c.parseFrom(s);
			case 1823640147:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_kick_out_c2s.parseFrom(s);
			case 1823655507:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_kick_out_s2c.parseFrom(s);
			case 1415766384:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_log_c2s.parseFrom(s);
			case 1415781744:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_log_s2c.parseFrom(s);
			case 363440852:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_members_change_s2c.parseFrom(s);
			case -1262717806:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_members_info_c2s.parseFrom(s);
			case -1262702446:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_members_info_s2c.parseFrom(s);
			case 1573703652:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_question_c2s.parseFrom(s);
			case -336822831:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_question_rank_s2c.parseFrom(s);
			case 1573719012:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_question_s2c.parseFrom(s);
			case -867003654:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_quick_join_c2s.parseFrom(s);
			case -2125132211:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_quit_c2s.parseFrom(s);
			case -2125116851:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_quit_s2c.parseFrom(s);
			case -555465907:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_rank_info_c2s.parseFrom(s);
			case -555450547:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_rank_info_s2c.parseFrom(s);
			case -1665634868:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_rank_my_info_c2s.parseFrom(s);
			case -1665619508:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_rank_my_info_s2c.parseFrom(s);
			case 73572909:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_resource_change_s2c.parseFrom(s);
			case -1386977771:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_schedule_c2s.parseFrom(s);
			case -1386962411:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_schedule_s2c.parseFrom(s);
			case -2098275610:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_search_c2s.parseFrom(s);
			case -2098260250:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_search_s2c.parseFrom(s);
			case -204133636:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_setting_c2s.parseFrom(s);
			case -204118276:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_setting_s2c.parseFrom(s);
			case -1412579333:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_show_c2s.parseFrom(s);
			case -1412563973:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_show_s2c.parseFrom(s);
			case -388107094:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_treasure_info_c2s.parseFrom(s);
			case -388091734:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_treasure_info_s2c.parseFrom(s);
			case -1070692282:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_treasure_open_c2s.parseFrom(s);
			case -1070676922:
				return org.gof.demo.worldsrv.msg.MsgGuild.guild_treasure_open_s2c.parseFrom(s);
			case 1991582396:
				return org.gof.demo.worldsrv.msg.MsgGuild.world_schedule_update_c2s.parseFrom(s);
			case 1991597756:
				return org.gof.demo.worldsrv.msg.MsgGuild.world_schedule_update_s2c.parseFrom(s);
			case 2109410369:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_combat_acc_s2c.parseFrom(s);
			case 1082914431:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_combat_c2s.parseFrom(s);
			case 1082929791:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_combat_s2c.parseFrom(s);
			case 1129250647:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_enter_c2s.parseFrom(s);
			case 1129266007:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_enter_s2c.parseFrom(s);
			case -534291159:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_exit_c2s.parseFrom(s);
			case -534275799:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_exit_s2c.parseFrom(s);
			case 1766375609:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_info_c2s.parseFrom(s);
			case 1766390969:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_info_s2c.parseFrom(s);
			case 1844492599:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_rank_c2s.parseFrom(s);
			case 1844507959:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_rank_s2c.parseFrom(s);
			case 894451496:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_result_s2c.parseFrom(s);
			case 1666166748:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_sign_up_c2s.parseFrom(s);
			case 1666182108:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_sign_up_s2c.parseFrom(s);
			case -316499376:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_skill_s2c.parseFrom(s);
			case -470780803:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_stage_s2c.parseFrom(s);
			case -142007327:
				return org.gof.demo.worldsrv.msg.MsgGuildBoss.guild_boss_start_s2c.parseFrom(s);
			case 1959423094:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_battle_result_c2s.parseFrom(s);
			case 1959438454:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_battle_result_s2c.parseFrom(s);
			case -107668269:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_battle_start_c2s.parseFrom(s);
			case -107652909:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_battle_start_s2c.parseFrom(s);
			case -1406248185:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_add_c2s.parseFrom(s);
			case -1406232825:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_add_s2c.parseFrom(s);
			case -1220536238:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_fight_end_c2s.parseFrom(s);
			case -1220520878:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_fight_end_s2c.parseFrom(s);
			case -178399271:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_fight_start_s2c.parseFrom(s);
			case 93327049:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_ready_c2s.parseFrom(s);
			case 93342409:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_ready_s2c.parseFrom(s);
			case 253852801:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_show_c2s.parseFrom(s);
			case 253868161:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_show_s2c.parseFrom(s);
			case -945585656:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_start_c2s.parseFrom(s);
			case -945570296:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_start_s2c.parseFrom(s);
			case 2041674739:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_boss_start_status_s2c.parseFrom(s);
			case 62179849:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_entrance_info_c2s.parseFrom(s);
			case 62195209:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_entrance_info_s2c.parseFrom(s);
			case 984761748:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_choose_c2s.parseFrom(s);
			case 984777108:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_choose_s2c.parseFrom(s);
			case -1859687896:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_end_c2s.parseFrom(s);
			case -1859672536:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_end_s2c.parseFrom(s);
			case 551955677:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_fight_c2s.parseFrom(s);
			case 551971037:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_fight_s2c.parseFrom(s);
			case 2100193947:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_list_c2s.parseFrom(s);
			case 2100209307:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_event_list_s2c.parseFrom(s);
			case -1689087020:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_life_share_c2s.parseFrom(s);
			case -1689071660:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_life_share_s2c.parseFrom(s);
			case 1323481172:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_mission_update_s2c.parseFrom(s);
			case 1490939787:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_obj_add_s2c.parseFrom(s);
			case -105499147:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_obj_del_s2c.parseFrom(s);
			case 106302289:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_obj_move_c2s.parseFrom(s);
			case 106317649:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_obj_move_s2c.parseFrom(s);
			case 696701231:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_obj_update_elem_s2c.parseFrom(s);
			case 36228489:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_obj_update_s2c.parseFrom(s);
			case -1181377515:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_result_s2c.parseFrom(s);
			case -2035600051:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_revive_c2s.parseFrom(s);
			case -2035584691:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_revive_s2c.parseFrom(s);
			case 1081675303:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_reward_c2s.parseFrom(s);
			case 1081690663:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_reward_s2c.parseFrom(s);
			case -1415493865:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_enter_c2s.parseFrom(s);
			case -1415478505:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_enter_s2c.parseFrom(s);
			case 1103791990:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_leave_c2s.parseFrom(s);
			case 1103807350:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_leave_s2c.parseFrom(s);
			case 1425954845:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_locate_c2s.parseFrom(s);
			case 1425970205:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_locate_s2c.parseFrom(s);
			case 57008461:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_member_update_s2c.parseFrom(s);
			case -1647854494:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_ready_c2s.parseFrom(s);
			case -1647839134:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_ready_s2c.parseFrom(s);
			case 1579851130:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_reward_s2c.parseFrom(s);
			case -506686075:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_tower_unlock_s2c.parseFrom(s);
			case -407295468:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_update_s2c.parseFrom(s);
			case -900037776:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_scene_view_c2s.parseFrom(s);
			case 1738424912:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_create_c2s.parseFrom(s);
			case 1738440272:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_create_s2c.parseFrom(s);
			case -347723470:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_exit_c2s.parseFrom(s);
			case -347708110:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_exit_s2c.parseFrom(s);
			case 1952943298:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_info_c2s.parseFrom(s);
			case 1952958658:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_info_s2c.parseFrom(s);
			case 373253950:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_join_c2s.parseFrom(s);
			case 373269310:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_join_s2c.parseFrom(s);
			case 911464026:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_kick_c2s.parseFrom(s);
			case 911479386:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_kick_s2c.parseFrom(s);
			case -1909446215:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_ready_c2s.parseFrom(s);
			case -1909430855:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_ready_s2c.parseFrom(s);
			case 1282842524:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_single_c2s.parseFrom(s);
			case 1282857884:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_single_s2c.parseFrom(s);
			case 1346608376:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_start_c2s.parseFrom(s);
			case 1346623736:
				return org.gof.demo.worldsrv.msg.MsgGve.gve_team_start_s2c.parseFrom(s);
			case 356987025:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_cancel_red_point_c2s.parseFrom(s);
			case 557420145:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_fight_info_c2s.parseFrom(s);
			case 557435505:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_fight_info_s2c.parseFrom(s);
			case 1232628855:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_fight_report_c2s.parseFrom(s);
			case 1232644215:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_fight_report_s2c.parseFrom(s);
			case -1005929748:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_fight_settlement_s2c.parseFrom(s);
			case 2113505389:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_hall_c2s.parseFrom(s);
			case 605026857:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_hall_of_fame_c2s.parseFrom(s);
			case 605042217:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_hall_of_fame_s2c.parseFrom(s);
			case 2113520749:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_hall_s2c.parseFrom(s);
			case -1955009950:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_info_c2s.parseFrom(s);
			case -1954994590:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_info_s2c.parseFrom(s);
			case 276528256:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_map_update_s2c.parseFrom(s);
			case -1445000924:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_play_video_c2s.parseFrom(s);
			case -1444985564:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_play_video_s2c.parseFrom(s);
			case -1876892960:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_rank_c2s.parseFrom(s);
			case -1270183993:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_rank_list_c2s.parseFrom(s);
			case -1270168633:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_rank_list_s2c.parseFrom(s);
			case -1876877600:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_rank_s2c.parseFrom(s);
			case 98075975:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_change_all_c2s.parseFrom(s);
			case 98091335:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_change_all_s2c.parseFrom(s);
			case 724114789:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_change_c2s.parseFrom(s);
			case 724130149:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_change_s2c.parseFrom(s);
			case -1664576669:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_info_c2s.parseFrom(s);
			case -1664561309:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_info_s2c.parseFrom(s);
			case -1734850050:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_road_update_s2c.parseFrom(s);
			case -89132352:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_season_info_c2s.parseFrom(s);
			case -89116992:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_season_info_s2c.parseFrom(s);
			case 1615332921:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_select_road_c2s.parseFrom(s);
			case 1615348281:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_select_road_s2c.parseFrom(s);
			case -1806775523:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_serv_list_c2s.parseFrom(s);
			case -1806760163:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_serv_list_s2c.parseFrom(s);
			case 141427887:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_settlement_season_s2c.parseFrom(s);
			case -913008896:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_settlement_week_s2c.parseFrom(s);
			case -22799987:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_week_rank_c2s.parseFrom(s);
			case -22784627:
				return org.gof.demo.worldsrv.msg.MsgGvg.gvg_week_rank_s2c.parseFrom(s);
			case 1551530846:
				return org.gof.demo.worldsrv.msg.MsgGvg.play_video_test_s2c.parseFrom(s);
			case -1976601807:
				return org.gof.demo.worldsrv.msg.MsgHeadFrame.head_frame_info_c2s.parseFrom(s);
			case -1976586447:
				return org.gof.demo.worldsrv.msg.MsgHeadFrame.head_frame_info_s2c.parseFrom(s);
			case -844030103:
				return org.gof.demo.worldsrv.msg.MsgHeadFrame.head_frame_red_point_c2s.parseFrom(s);
			case -844014743:
				return org.gof.demo.worldsrv.msg.MsgHeadFrame.head_frame_red_point_s2c.parseFrom(s);
			case 1409932674:
				return org.gof.demo.worldsrv.msg.MsgHeadFrame.head_frame_wear_c2s.parseFrom(s);
			case 1409948034:
				return org.gof.demo.worldsrv.msg.MsgHeadFrame.head_frame_wear_s2c.parseFrom(s);
			case 207433529:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_begin_c2s.parseFrom(s);
			case -1648153202:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_begin_fail_s2c.parseFrom(s);
			case 207448889:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_begin_s2c.parseFrom(s);
			case 479637273:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_report_list_c2s.parseFrom(s);
			case 479652633:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_report_list_s2c.parseFrom(s);
			case 452314807:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_result_c2s.parseFrom(s);
			case 452330167:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_battle_result_s2c.parseFrom(s);
			case -664142949:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_building_lev_up_c2s.parseFrom(s);
			case -664127589:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_building_lev_up_s2c.parseFrom(s);
			case -58687261:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_fertilize_c2s.parseFrom(s);
			case -58671901:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_fertilize_s2c.parseFrom(s);
			case -821209956:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_harvest_c2s.parseFrom(s);
			case -821194596:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_harvest_s2c.parseFrom(s);
			case 903755561:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_human_check_c2s.parseFrom(s);
			case -568746403:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_human_check_result_c2s.parseFrom(s);
			case -568731043:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_human_check_result_s2c.parseFrom(s);
			case 903770921:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_human_check_s2c.parseFrom(s);
			case -1377739387:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_info_c2s.parseFrom(s);
			case -1377724027:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_info_s2c.parseFrom(s);
			case 1244898064:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_log_list_c2s.parseFrom(s);
			case 1244913424:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_log_list_s2c.parseFrom(s);
			case 1999865720:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_pick_c2s.parseFrom(s);
			case 1999881080:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_pick_s2c.parseFrom(s);
			case 1129958942:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_plant_c2s.parseFrom(s);
			case 1129974302:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_plant_s2c.parseFrom(s);
			case 106911239:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_play_video_c2s.parseFrom(s);
			case 106926599:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_play_video_s2c.parseFrom(s);
			case -1213175544:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_search_list_c2s.parseFrom(s);
			case -1213160184:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_search_list_s2c.parseFrom(s);
			case -894611813:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_search_refresh_c2s.parseFrom(s);
			case -894596453:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_search_refresh_s2c.parseFrom(s);
			case -324159706:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_spend_fruit_c2s.parseFrom(s);
			case -324144346:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_spend_fruit_s2c.parseFrom(s);
			case 1001068021:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_attr_lock_c2s.parseFrom(s);
			case 1001083381:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_attr_lock_s2c.parseFrom(s);
			case 1487990364:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_info_c2s.parseFrom(s);
			case 1488005724:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_info_s2c.parseFrom(s);
			case 1315389143:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_refresh_c2s.parseFrom(s);
			case -192981364:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_tab_change_name_c2s.parseFrom(s);
			case -192966004:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_tab_change_name_s2c.parseFrom(s);
			case -293847847:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_tab_use_c2s.parseFrom(s);
			case -293832487:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_statue_tab_use_s2c.parseFrom(s);
			case 2102348123:
				return org.gof.demo.worldsrv.msg.MsgHome.home_farm_update_self_stolen_s2c.parseFrom(s);
			case -592445136:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_auto_settle_c2s.parseFrom(s);
			case -592429776:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_auto_settle_s2c.parseFrom(s);
			case -1302238284:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_cast_c2s.parseFrom(s);
			case -1302222924:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_cast_s2c.parseFrom(s);
			case -620171893:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_daily_change_s2c.parseFrom(s);
			case 1268236632:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_daily_task_reward_c2s.parseFrom(s);
			case 1268251992:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_daily_task_reward_s2c.parseFrom(s);
			case 451654303:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_data_c2s.parseFrom(s);
			case 451669663:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_data_s2c.parseFrom(s);
			case 1106609612:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_detail_update_s2c.parseFrom(s);
			case 1330728920:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_fin_auto_c2s.parseFrom(s);
			case 1330744280:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_fin_auto_s2c.parseFrom(s);
			case -1420303043:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_get_album_reward_c2s.parseFrom(s);
			case -1420287683:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_get_album_reward_s2c.parseFrom(s);
			case -707675183:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_ground_unlock_c2s.parseFrom(s);
			case -707659823:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_ground_unlock_s2c.parseFrom(s);
			case -1334092821:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_group_level_up_c2s.parseFrom(s);
			case -1334077461:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_group_level_up_s2c.parseFrom(s);
			case -1075938227:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_design_level_up_c2s.parseFrom(s);
			case -1075922867:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_design_level_up_s2c.parseFrom(s);
			case 1948603046:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_equip_c2s.parseFrom(s);
			case 551381590:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_equip_one_click_c2s.parseFrom(s);
			case 551396950:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_equip_one_click_s2c.parseFrom(s);
			case 1948618406:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_equip_s2c.parseFrom(s);
			case 1873014119:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_set_use_design_c2s.parseFrom(s);
			case 1873029479:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_house_set_use_design_s2c.parseFrom(s);
			case -2083035889:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_reel_c2s.parseFrom(s);
			case -2083020529:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_reel_s2c.parseFrom(s);
			case 820585825:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_start_auto_c2s.parseFrom(s);
			case 820601185:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_start_auto_s2c.parseFrom(s);
			case -780629122:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_tool_lv_up_c2s.parseFrom(s);
			case -780613762:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_tool_lv_up_s2c.parseFrom(s);
			case 59415661:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_unlock_house_slot_c2s.parseFrom(s);
			case 59431021:
				return org.gof.demo.worldsrv.msg.MsgHome.home_fish_unlock_house_slot_s2c.parseFrom(s);
			case -1178736856:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_auto_use_goods_c2s.parseFrom(s);
			case -1178721496:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_auto_use_goods_s2c.parseFrom(s);
			case -435270446:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_get_reward_c2s.parseFrom(s);
			case -435255086:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_get_reward_s2c.parseFrom(s);
			case 2066047496:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_info_c2s.parseFrom(s);
			case 2066062856:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_info_s2c.parseFrom(s);
			case -267690232:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_update_recover_s2c.parseFrom(s);
			case -2145018066:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_use_goods_c2s.parseFrom(s);
			case -2145002706:
				return org.gof.demo.worldsrv.msg.MsgHome.home_mine_use_goods_s2c.parseFrom(s);
			case -1350439777:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_change_tab_name_c2s.parseFrom(s);
			case -1350424417:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_change_tab_name_s2c.parseFrom(s);
			case 1987318438:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_choose_c2s.parseFrom(s);
			case 1987333798:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_choose_s2c.parseFrom(s);
			case -168223236:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_choose_tab_c2s.parseFrom(s);
			case -168207876:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_choose_tab_s2c.parseFrom(s);
			case -2127921091:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_info_c2s.parseFrom(s);
			case -2127905731:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_info_s2c.parseFrom(s);
			case -1328481457:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_item_transform_c2s.parseFrom(s);
			case -1276752736:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_reset_tab_c2s.parseFrom(s);
			case -1276737376:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_reset_tab_s2c.parseFrom(s);
			case 1314706455:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_upgrade_c2s.parseFrom(s);
			case 1314721815:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_upgrade_s2c.parseFrom(s);
			case -774664242:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_wakeup_c2s.parseFrom(s);
			case -774648882:
				return org.gof.demo.worldsrv.msg.MsgJobsWakeup.jobs_wakeup_wakeup_s2c.parseFrom(s);
			case 840996982:
				return org.gof.demo.worldsrv.msg.MsgJsonProto.json_proto_c2s.parseFrom(s);
			case 841012342:
				return org.gof.demo.worldsrv.msg.MsgJsonProto.json_proto_s2c.parseFrom(s);
			case 1529781731:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_bet_history_c2s.parseFrom(s);
			case 1529797091:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_bet_history_s2c.parseFrom(s);
			case -471819152:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_bet_c2s.parseFrom(s);
			case -471803792:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_bet_s2c.parseFrom(s);
			case 711635049:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_champion_history_c2s.parseFrom(s);
			case 711650409:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_champion_history_s2c.parseFrom(s);
			case 686182563:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_champion_info_c2s.parseFrom(s);
			case 686197923:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_champion_info_s2c.parseFrom(s);
			case 1644010646:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_enemys_c2s.parseFrom(s);
			case 1644026006:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_enemys_s2c.parseFrom(s);
			case 879738713:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_info_c2s.parseFrom(s);
			case 879754073:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_info_s2c.parseFrom(s);
			case 1812582309:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_knockout_c2s.parseFrom(s);
			case 1812597669:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_knockout_s2c.parseFrom(s);
			case 1111868581:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_lookup_c2s.parseFrom(s);
			case -1692656390:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_lookup_role_c2s.parseFrom(s);
			case -1692641030:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_lookup_role_s2c.parseFrom(s);
			case 1111883941:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_lookup_s2c.parseFrom(s);
			case -1783622858:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_loop_bet_info_c2s.parseFrom(s);
			case -1783607498:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_loop_bet_info_s2c.parseFrom(s);
			case -1335675313:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_loop_c2s.parseFrom(s);
			case -1946409402:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_loop_rank_c2s.parseFrom(s);
			case -1946394042:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_loop_rank_s2c.parseFrom(s);
			case -1335659953:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_loop_s2c.parseFrom(s);
			case 1212813877:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_null_list_c2s.parseFrom(s);
			case 1212829237:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_null_list_s2c.parseFrom(s);
			case 286453876:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_pet_plan_c2s.parseFrom(s);
			case 286469236:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_pet_plan_s2c.parseFrom(s);
			case -175503141:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_play_video_c2s.parseFrom(s);
			case 1212276843:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_play_video_result_c2s.parseFrom(s);
			case -175487781:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_play_video_s2c.parseFrom(s);
			case -646301517:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_pos_c2s.parseFrom(s);
			case -646286157:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_pos_s2c.parseFrom(s);
			case 957855703:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_rank_c2s.parseFrom(s);
			case 957871063:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_rank_s2c.parseFrom(s);
			case 1743127391:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_report_c2s.parseFrom(s);
			case 1743142751:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_report_s2c.parseFrom(s);
			case 611207882:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_req_list_c2s.parseFrom(s);
			case 611223242:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_req_list_s2c.parseFrom(s);
			case 1891951881:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_req_team_c2s.parseFrom(s);
			case 1891967241:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_req_team_s2c.parseFrom(s);
			case 1782198455:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_resp_invite_c2s.parseFrom(s);
			case 1782213815:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_resp_invite_s2c.parseFrom(s);
			case 176792036:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_resp_role_c2s.parseFrom(s);
			case 176807396:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_resp_role_s2c.parseFrom(s);
			case -480083339:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_combat_c2s.parseFrom(s);
			case -480067979:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_combat_s2c.parseFrom(s);
			case 1272504445:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_create_c2s.parseFrom(s);
			case 1272519805:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_create_s2c.parseFrom(s);
			case 603745951:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_exit_c2s.parseFrom(s);
			case 603761311:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_exit_s2c.parseFrom(s);
			case -1390554577:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_info_c2s.parseFrom(s);
			case -1390539217:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_info_s2c.parseFrom(s);
			case 1324723371:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_join_c2s.parseFrom(s);
			case 1324738731:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_join_s2c.parseFrom(s);
			case -1348106817:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_rename_c2s.parseFrom(s);
			case -1348091457:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_rename_s2c.parseFrom(s);
			case -668561634:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_result_c2s.parseFrom(s);
			case -668546274:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_result_s2c.parseFrom(s);
			case -392640054:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_update_c2s.parseFrom(s);
			case -392624694:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_team_update_s2c.parseFrom(s);
			case -1375566344:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_time_c2s.parseFrom(s);
			case -1375550984:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_time_s2c.parseFrom(s);
			case -1385647563:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_worship_c2s.parseFrom(s);
			case -1385632203:
				return org.gof.demo.worldsrv.msg.MsgKungfuRace.kungfu_race_worship_s2c.parseFrom(s);
			case 1451121985:
				return org.gof.demo.worldsrv.msg.MsgLogin.cheat_c2s.parseFrom(s);
			case -1779917045:
				return org.gof.demo.worldsrv.msg.MsgLogin.heart_beat_c2s.parseFrom(s);
			case -1779901685:
				return org.gof.demo.worldsrv.msg.MsgLogin.heart_beat_s2c.parseFrom(s);
			case -1389725638:
				return org.gof.demo.worldsrv.msg.MsgLogin.login_auth_c2s.parseFrom(s);
			case -1389710278:
				return org.gof.demo.worldsrv.msg.MsgLogin.login_auth_s2c.parseFrom(s);
			case -722024826:
				return org.gof.demo.worldsrv.msg.MsgLogin.logout_s2c.parseFrom(s);
			case -1179182884:
				return org.gof.demo.worldsrv.msg.MsgLogin.role_login_c2s.parseFrom(s);
			case -1179167524:
				return org.gof.demo.worldsrv.msg.MsgLogin.role_login_s2c.parseFrom(s);
			case -883101133:
				return org.gof.demo.worldsrv.msg.MsgLogin.role_login_trial_c2s.parseFrom(s);
			case -883085773:
				return org.gof.demo.worldsrv.msg.MsgLogin.role_login_trial_s2c.parseFrom(s);
			case -391829814:
				return org.gof.demo.worldsrv.msg.MsgLogin.role_reconnect_c2s.parseFrom(s);
			case -391814454:
				return org.gof.demo.worldsrv.msg.MsgLogin.role_reconnect_s2c.parseFrom(s);
			case -2109036630:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_claim_c2s.parseFrom(s);
			case -2109021270:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_claim_s2c.parseFrom(s);
			case 1557008039:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_delete_c2s.parseFrom(s);
			case 1557023399:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_delete_s2c.parseFrom(s);
			case -145906555:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_expired_reward_c2s.parseFrom(s);
			case -145891195:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_expired_reward_s2c.parseFrom(s);
			case 1010557402:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_list_c2s.parseFrom(s);
			case 1010572762:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_list_s2c.parseFrom(s);
			case 449249614:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_new_s2c.parseFrom(s);
			case -1202630958:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_read_c2s.parseFrom(s);
			case -1202615598:
				return org.gof.demo.worldsrv.msg.MsgMail.mail_read_s2c.parseFrom(s);
			case -1013849821:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.parseFrom(s);
			case -1013834461:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.parseFrom(s);
			case 587560337:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.parseFrom(s);
			case 587575697:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.parseFrom(s);
			case 640523071:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.parseFrom(s);
			case 640538431:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.parseFrom(s);
			case -82944279:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.parseFrom(s);
			case -82928919:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.parseFrom(s);
			case 1281905710:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.parseFrom(s);
			case 1281921070:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.parseFrom(s);
			case -126734217:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.parseFrom(s);
			case -126718857:
				return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.parseFrom(s);
			case -976025003:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_buy_flower_c2s.parseFrom(s);
			case -976009643:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_buy_flower_s2c.parseFrom(s);
			case -685968808:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_friend_info_c2s.parseFrom(s);
			case -685953448:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_friend_info_s2c.parseFrom(s);
			case -573580684:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_friend_lv_c2s.parseFrom(s);
			case -573565324:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_friend_lv_s2c.parseFrom(s);
			case 1565406962:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_give_flower_c2s.parseFrom(s);
			case 1565422322:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_give_flower_s2c.parseFrom(s);
			case 988922827:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_reward_fetch_c2s.parseFrom(s);
			case -719765273:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_reward_info_c2s.parseFrom(s);
			case -719749913:
				return org.gof.demo.worldsrv.msg.MsgMarry.favor_reward_info_s2c.parseFrom(s);
			case 313304821:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_break_up_c2s.parseFrom(s);
			case 313320181:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_break_up_s2c.parseFrom(s);
			case 1265137451:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_cancel_divorce_c2s.parseFrom(s);
			case 1265152811:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_cancel_divorce_s2c.parseFrom(s);
			case -628579351:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_cancel_wedding_c2s.parseFrom(s);
			case -628563991:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_cancel_wedding_s2c.parseFrom(s);
			case 1781288771:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_direct_c2s.parseFrom(s);
			case 1781304131:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_direct_s2c.parseFrom(s);
			case -773924014:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_divorce_force_c2s.parseFrom(s);
			case -773908654:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_divorce_force_s2c.parseFrom(s);
			case -1384910733:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_divorce_resp_c2s.parseFrom(s);
			case -1384895373:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_divorce_resp_s2c.parseFrom(s);
			case -2049284124:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_invite_friend_c2s.parseFrom(s);
			case -2049268764:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_invite_friend_s2c.parseFrom(s);
			case -1286352464:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_mark_info_c2s.parseFrom(s);
			case -1286337104:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_mark_info_s2c.parseFrom(s);
			case 1073724459:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_action_all_c2s.parseFrom(s);
			case 1073739819:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_action_all_s2c.parseFrom(s);
			case -1859688887:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_action_c2s.parseFrom(s);
			case -1859673527:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_action_s2c.parseFrom(s);
			case 153906167:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_figure_c2s.parseFrom(s);
			case 153921527:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_figure_s2c.parseFrom(s);
			case 2083689771:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_fire_val_c2s.parseFrom(s);
			case 2083705131:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_fire_val_s2c.parseFrom(s);
			case 786687041:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_info_c2s.parseFrom(s);
			case 786702401:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_info_s2c.parseFrom(s);
			case -1674895493:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_main_answer_c2s.parseFrom(s);
			case -1674880133:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_main_answer_s2c.parseFrom(s);
			case 1950142883:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_main_question_c2s.parseFrom(s);
			case 1950158243:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_main_question_s2c.parseFrom(s);
			case 833701673:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_photo_c2s.parseFrom(s);
			case 833717033:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_photo_s2c.parseFrom(s);
			case -664017869:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_reserve_c2s.parseFrom(s);
			case 626680404:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_reserve_list_c2s.parseFrom(s);
			case 626695764:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_reserve_list_s2c.parseFrom(s);
			case -664002509:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_reserve_s2c.parseFrom(s);
			case 1403401935:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_resp_invite_c2s.parseFrom(s);
			case 1403417295:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_resp_invite_s2c.parseFrom(s);
			case -776370699:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_stage_c2s.parseFrom(s);
			case -776355339:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_party_stage_s2c.parseFrom(s);
			case 62100786:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_photo_album_c2s.parseFrom(s);
			case 62116146:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_photo_album_s2c.parseFrom(s);
			case -875504406:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_resp_c2s.parseFrom(s);
			case -875489046:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_resp_s2c.parseFrom(s);
			case 1958186413:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_info_c2s.parseFrom(s);
			case 1958201773:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_info_s2c.parseFrom(s);
			case -1850992765:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_levup_c2s.parseFrom(s);
			case -1850977405:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_levup_s2c.parseFrom(s);
			case 186814866:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_use_c2s.parseFrom(s);
			case 186830226:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_use_s2c.parseFrom(s);
			case 1330982948:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_use_skill_c2s.parseFrom(s);
			case 1330998308:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_ring_use_skill_s2c.parseFrom(s);
			case 1163625807:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_box_info_c2s.parseFrom(s);
			case 1163641167:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_box_info_s2c.parseFrom(s);
			case 481040619:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_box_open_c2s.parseFrom(s);
			case 481055979:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_box_open_s2c.parseFrom(s);
			case -852830114:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_broadcast_s2c.parseFrom(s);
			case 364311285:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_enter_c2s.parseFrom(s);
			case 364326645:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_enter_s2c.parseFrom(s);
			case 826506699:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_exit_c2s.parseFrom(s);
			case 826522059:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_exit_s2c.parseFrom(s);
			case -1450152194:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_move_c2s.parseFrom(s);
			case -1450136834:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_move_s2c.parseFrom(s);
			case -523986785:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_status_c2s.parseFrom(s);
			case -523971425:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_scene_status_s2c.parseFrom(s);
			case 1805473484:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_status_c2s.parseFrom(s);
			case 1805488844:
				return org.gof.demo.worldsrv.msg.MsgMarry.marry_status_s2c.parseFrom(s);
			case -695942432:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_info_c2s.parseFrom(s);
			case -695927072:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_info_s2c.parseFrom(s);
			case 1770358960:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_levup_c2s.parseFrom(s);
			case 1770374320:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_levup_s2c.parseFrom(s);
			case -525649530:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_talent_up_c2s.parseFrom(s);
			case -525634170:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_talent_up_s2c.parseFrom(s);
			case -1060077927:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_up_skin_c2s.parseFrom(s);
			case -1060062567:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_up_skin_s2c.parseFrom(s);
			case 655387135:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_use_c2s.parseFrom(s);
			case 655402495:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_use_s2c.parseFrom(s);
			case -846708015:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_use_skill_c2s.parseFrom(s);
			case -846692655:
				return org.gof.demo.worldsrv.msg.MsgMount.mount_use_skill_s2c.parseFrom(s);
			case -2059119783:
				return org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.parseFrom(s);
			case -2059104423:
				return org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.parseFrom(s);
			case -1393385998:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.parseFrom(s);
			case 325389096:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.parseFrom(s);
			case 325404456:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.parseFrom(s);
			case 249489874:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.parseFrom(s);
			case 249505234:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.parseFrom(s);
			case 1583639272:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.parseFrom(s);
			case -638196605:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.parseFrom(s);
			case 963176532:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.parseFrom(s);
			case 963191892:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.parseFrom(s);
			case 1075100236:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.parseFrom(s);
			case 1075115596:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.parseFrom(s);
			case 575503914:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.parseFrom(s);
			case 575519274:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.parseFrom(s);
			case 1191106155:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.parseFrom(s);
			case 1191121515:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.parseFrom(s);
			case -74320872:
				return org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.parseFrom(s);
			case -653191054:
				return org.gof.demo.worldsrv.msg.MsgPayMall.fake_recharge_c2s.parseFrom(s);
			case -1826904387:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_gift_draw_c2s.parseFrom(s);
			case -1826889027:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_gift_draw_s2c.parseFrom(s);
			case 1259084401:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_gift_new_s2c.parseFrom(s);
			case 1847843069:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_gift_remove_s2c.parseFrom(s);
			case 954294918:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_check_push_c2s.parseFrom(s);
			case -2052621760:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_custom_mall_info_c2s.parseFrom(s);
			case -2052606400:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_custom_mall_info_s2c.parseFrom(s);
			case -164866054:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_custom_mall_set_c2s.parseFrom(s);
			case -164850694:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_custom_mall_set_s2c.parseFrom(s);
			case 60144835:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_info_c2s.parseFrom(s);
			case 60160195:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_info_s2c.parseFrom(s);
			case -156953028:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_recharge_s2c.parseFrom(s);
			case -147729724:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_reward_c2s.parseFrom(s);
			case -147714364:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_reward_s2c.parseFrom(s);
			case -266257703:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_rewards_c2s.parseFrom(s);
			case -266242343:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_rewards_s2c.parseFrom(s);
			case -2134860962:
				return org.gof.demo.worldsrv.msg.MsgPayMall.pay_mall_update_s2c.parseFrom(s);
			case -1321648175:
				return org.gof.demo.worldsrv.msg.MsgPayMall.role_pay_check_c2s.parseFrom(s);
			case -1321632815:
				return org.gof.demo.worldsrv.msg.MsgPayMall.role_pay_check_s2c.parseFrom(s);
			case 538415407:
				return org.gof.demo.worldsrv.msg.MsgPayMall.role_pay_info_s2c.parseFrom(s);
			case -696798628:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_get_gift_list_c2s.parseFrom(s);
			case -696783268:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_get_gift_list_s2c.parseFrom(s);
			case -1354568793:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_get_msg_info_c2s.parseFrom(s);
			case -1354553433:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_get_msg_info_s2c.parseFrom(s);
			case 1679891170:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_gift_c2s.parseFrom(s);
			case -105169312:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_gift_history_info_c2s.parseFrom(s);
			case -105153952:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_gift_history_info_s2c.parseFrom(s);
			case 1679906530:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_gift_s2c.parseFrom(s);
			case 2001899850:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_notice_s2c.parseFrom(s);
			case 205406680:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_remove_gift_history_c2s.parseFrom(s);
			case 205422040:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_remove_gift_history_s2c.parseFrom(s);
			case 76555223:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_return_gift_c2s.parseFrom(s);
			case 76570583:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_return_gift_s2c.parseFrom(s);
			case 667421217:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_reward_c2s.parseFrom(s);
			case 667436577:
				return org.gof.demo.worldsrv.msg.MsgPayMall.show_mall_reward_s2c.parseFrom(s);
			case 1595578984:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_change_tab_name_c2s.parseFrom(s);
			case 1595594344:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_change_tab_name_s2c.parseFrom(s);
			case 832255635:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_choose_tab_c2s.parseFrom(s);
			case 832270995:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_choose_tab_s2c.parseFrom(s);
			case 770710524:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_levup_c2s.parseFrom(s);
			case 162425348:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_list_c2s.parseFrom(s);
			case 162440708:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_list_s2c.parseFrom(s);
			case -1877911907:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_set_all_pos_c2s.parseFrom(s);
			case -1963296709:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_set_pos_c2s.parseFrom(s);
			case -1963281349:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_set_pos_s2c.parseFrom(s);
			case -748885100:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_skin_equip_c2s.parseFrom(s);
			case -2101552927:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_skin_up_c2s.parseFrom(s);
			case 971790078:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_tab_info_c2s.parseFrom(s);
			case 971805438:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_tab_info_s2c.parseFrom(s);
			case -1853396076:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_tab_info_update_s2c.parseFrom(s);
			case 225368575:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_unlock_pos_s2c.parseFrom(s);
			case -78097046:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_unlock_s2c.parseFrom(s);
			case -928800659:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_update_part_s2c.parseFrom(s);
			case 485385327:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_update_s2c.parseFrom(s);
			case -1196510149:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_use_proficiency_item_c2s.parseFrom(s);
			case -1196494789:
				return org.gof.demo.worldsrv.msg.MsgPet.pet_use_proficiency_item_s2c.parseFrom(s);
			case 290508811:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_card_get_all_c2s.parseFrom(s);
			case 290524171:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_card_get_all_s2c.parseFrom(s);
			case 2040973413:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_card_info_c2s.parseFrom(s);
			case 2040988773:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_card_info_s2c.parseFrom(s);
			case 758021606:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_card_reward_c2s.parseFrom(s);
			case 758036966:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_card_reward_s2c.parseFrom(s);
			case -323971024:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_info_c2s.parseFrom(s);
			case -323955664:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_info_s2c.parseFrom(s);
			case -1903014005:
				return org.gof.demo.worldsrv.msg.MsgPrivilege.privilege_update_s2c.parseFrom(s);
			case 730212461:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.parseFrom(s);
			case 730227821:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.parseFrom(s);
			case 317721761:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.parseFrom(s);
			case 317737121:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.parseFrom(s);
			case -1353302221:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.parseFrom(s);
			case 337056612:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.parseFrom(s);
			case 337071972:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.parseFrom(s);
			case 195093941:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.parseFrom(s);
			case 195109301:
				return org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.parseFrom(s);
			case 158706430:
				return org.gof.demo.worldsrv.msg.MsgRed.red_all_list_c2s.parseFrom(s);
			case 158721790:
				return org.gof.demo.worldsrv.msg.MsgRed.red_all_list_s2c.parseFrom(s);
			case -475984635:
				return org.gof.demo.worldsrv.msg.MsgRed.red_brief_list_c2s.parseFrom(s);
			case -475969275:
				return org.gof.demo.worldsrv.msg.MsgRed.red_brief_list_s2c.parseFrom(s);
			case 784275278:
				return org.gof.demo.worldsrv.msg.MsgRed.red_grab_c2s.parseFrom(s);
			case 784290638:
				return org.gof.demo.worldsrv.msg.MsgRed.red_grab_s2c.parseFrom(s);
			case 1706096121:
				return org.gof.demo.worldsrv.msg.MsgRed.red_pop_s2c.parseFrom(s);
			case 1944542858:
				return org.gof.demo.worldsrv.msg.MsgRed.red_send_c2s.parseFrom(s);
			case 1944558218:
				return org.gof.demo.worldsrv.msg.MsgRed.red_send_s2c.parseFrom(s);
			case -1015916557:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.parseFrom(s);
			case 646723408:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.parseFrom(s);
			case 646738768:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.parseFrom(s);
			case 293939371:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.parseFrom(s);
			case 293954731:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.parseFrom(s);
			case 951839132:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.parseFrom(s);
			case 951854492:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.parseFrom(s);
			case 1533873591:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.parseFrom(s);
			case 1533888951:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.parseFrom(s);
			case -1903953364:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.parseFrom(s);
			case -1903938004:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.parseFrom(s);
			case -1803168204:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.parseFrom(s);
			case -1803152844:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.parseFrom(s);
			case -1218711786:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.parseFrom(s);
			case -1218696426:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.parseFrom(s);
			case 1492715644:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.parseFrom(s);
			case -411117694:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.parseFrom(s);
			case -411102334:
				return org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.parseFrom(s);
			case 1361610731:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_act_rebate_info_c2s.parseFrom(s);
			case 1361626091:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_act_rebate_info_s2c.parseFrom(s);
			case 725513196:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_act_rebate_reward_c2s.parseFrom(s);
			case 725528556:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_act_rebate_reward_s2c.parseFrom(s);
			case 1001578173:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_bind_code_c2s.parseFrom(s);
			case 1001593533:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_bind_code_s2c.parseFrom(s);
			case -1872970559:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_bind_old_c2s.parseFrom(s);
			case -1872955199:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_bind_old_s2c.parseFrom(s);
			case 1287497602:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_bind_status_c2s.parseFrom(s);
			case 1287512962:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_bind_status_s2c.parseFrom(s);
			case 802071888:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_can_bind_old_c2s.parseFrom(s);
			case 802087248:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_can_bind_old_s2c.parseFrom(s);
			case -1640322532:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_checkin_info_c2s.parseFrom(s);
			case -1640307172:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_checkin_info_s2c.parseFrom(s);
			case 2085670365:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_checkin_reward_c2s.parseFrom(s);
			case 2085685725:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_checkin_reward_s2c.parseFrom(s);
			case -1880223629:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_choose_c2s.parseFrom(s);
			case -1880208269:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_choose_s2c.parseFrom(s);
			case 1488162122:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_info_c2s.parseFrom(s);
			case 1488177482:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_info_s2c.parseFrom(s);
			case 2157905:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_new_role_c2s.parseFrom(s);
			case 2173265:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_new_role_s2c.parseFrom(s);
			case -2029387566:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_new_serv_push_c2s.parseFrom(s);
			case -2029372206:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_new_serv_push_s2c.parseFrom(s);
			case -1602325291:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_role_list_c2s.parseFrom(s);
			case -1602309931:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_role_list_s2c.parseFrom(s);
			case 900078241:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_task_c2s.parseFrom(s);
			case -1012067849:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_task_reward_c2s.parseFrom(s);
			case -1012052489:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_task_reward_s2c.parseFrom(s);
			case 900093601:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_task_s2c.parseFrom(s);
			case 1295752849:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_task_update_c2s.parseFrom(s);
			case 1295768209:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_task_update_s2c.parseFrom(s);
			case -571288466:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_wheel_info_c2s.parseFrom(s);
			case -571273106:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_wheel_info_s2c.parseFrom(s);
			case 1536916994:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_wheel_spin_c2s.parseFrom(s);
			case 1536932354:
				return org.gof.demo.worldsrv.msg.MsgReturn.return_wheel_spin_s2c.parseFrom(s);
			case 861939043:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_attr_up_c2s.parseFrom(s);
			case 861954403:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_attr_up_s2c.parseFrom(s);
			case 530023618:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_bag_c2s.parseFrom(s);
			case 530038978:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_bag_s2c.parseFrom(s);
			case -440966567:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_buy_c2s.parseFrom(s);
			case -440951207:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_buy_s2c.parseFrom(s);
			case 572141518:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_choose_c2s.parseFrom(s);
			case 572156878:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_choose_s2c.parseFrom(s);
			case -1834876373:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_combat_c2s.parseFrom(s);
			case -1834861013:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_combat_s2c.parseFrom(s);
			case 842972891:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_enemy_c2s.parseFrom(s);
			case 842988251:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_enemy_s2c.parseFrom(s);
			case 1035128363:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_enter_c2s.parseFrom(s);
			case 1035143723:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_enter_s2c.parseFrom(s);
			case -1922800683:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_exit_c2s.parseFrom(s);
			case -1922785323:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_exit_s2c.parseFrom(s);
			case -662437250:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_meet_c2s.parseFrom(s);
			case -662421890:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_meet_s2c.parseFrom(s);
			case -2023354668:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_result_c2s.parseFrom(s);
			case -2023339308:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_result_s2c.parseFrom(s);
			case -145368659:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_shop_c2s.parseFrom(s);
			case -145353299:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_shop_s2c.parseFrom(s);
			case -476394511:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_type_c2s.parseFrom(s);
			case -476379151:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_branch_type_s2c.parseFrom(s);
			case -1545969161:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_copy_skill_c2s.parseFrom(s);
			case -1545953801:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_copy_skill_s2c.parseFrom(s);
			case 1499319902:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_info_c2s.parseFrom(s);
			case 1499335262:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_info_s2c.parseFrom(s);
			case -1358020876:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_combat_c2s.parseFrom(s);
			case -1358005516:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_combat_s2c.parseFrom(s);
			case 634868802:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_enter_c2s.parseFrom(s);
			case 634884162:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_enter_s2c.parseFrom(s);
			case 439023508:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_over_c2s.parseFrom(s);
			case 439038868:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_over_s2c.parseFrom(s);
			case -1546499171:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_result_c2s.parseFrom(s);
			case -1546483811:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_main_result_s2c.parseFrom(s);
			case -1949714207:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_rank_report_c2s.parseFrom(s);
			case -1949698847:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_rank_report_s2c.parseFrom(s);
			case -519194532:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_red_point_c2s.parseFrom(s);
			case -519179172:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_red_point_s2c.parseFrom(s);
			case 535515279:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_report_collect_c2s.parseFrom(s);
			case 535530639:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_report_collect_s2c.parseFrom(s);
			case 689501305:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_reports_c2s.parseFrom(s);
			case 689516665:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_reports_s2c.parseFrom(s);
			case -998920825:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_role_sp_cmp_c2s.parseFrom(s);
			case -998905465:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_role_sp_cmp_s2c.parseFrom(s);
			case 2091150014:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_science_finish_s2c.parseFrom(s);
			case 165829337:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_science_info_c2s.parseFrom(s);
			case 165844697:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_science_info_s2c.parseFrom(s);
			case 734602822:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_science_research_c2s.parseFrom(s);
			case 1638771758:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_science_reset_c2s.parseFrom(s);
			case 1638787118:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_science_reset_s2c.parseFrom(s);
			case 927602478:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_set_skill_c2s.parseFrom(s);
			case 927617838:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_set_skill_s2c.parseFrom(s);
			case -1136665939:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_skill_ext_c2s.parseFrom(s);
			case -1136650579:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_skill_ext_s2c.parseFrom(s);
			case -1523167006:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_status_c2s.parseFrom(s);
			case -1523151646:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_status_s2c.parseFrom(s);
			case 533143415:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_truntable_count_reward_c2s.parseFrom(s);
			case 533158775:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_truntable_count_reward_s2c.parseFrom(s);
			case -1914783524:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_truntable_draw_c2s.parseFrom(s);
			case -1914768164:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_truntable_draw_s2c.parseFrom(s);
			case -909956282:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_truntable_info_c2s.parseFrom(s);
			case -909940922:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_truntable_info_s2c.parseFrom(s);
			case 1671314580:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_week_reward_c2s.parseFrom(s);
			case 1671329940:
				return org.gof.demo.worldsrv.msg.MsgRogue.rogue_week_reward_s2c.parseFrom(s);
			case -1459918767:
				return org.gof.demo.worldsrv.msg.MsgRole.client_data_c2s.parseFrom(s);
			case -1459903407:
				return org.gof.demo.worldsrv.msg.MsgRole.client_data_s2c.parseFrom(s);
			case -1702443481:
				return org.gof.demo.worldsrv.msg.MsgRole.client_log_c2s.parseFrom(s);
			case -301954773:
				return org.gof.demo.worldsrv.msg.MsgRole.role_appeal_c2s.parseFrom(s);
			case -301939413:
				return org.gof.demo.worldsrv.msg.MsgRole.role_appeal_s2c.parseFrom(s);
			case -679493468:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_default_plan_c2s.parseFrom(s);
			case -1730156034:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_gender_c2s.parseFrom(s);
			case -1730140674:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_gender_s2c.parseFrom(s);
			case 456905629:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_head_c2s.parseFrom(s);
			case 414246730:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_job_c2s.parseFrom(s);
			case 693876078:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_plan_name_c2s.parseFrom(s);
			case 693891438:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_plan_name_s2c.parseFrom(s);
			case -1348097109:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_req_c2s.parseFrom(s);
			case -578693958:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_skin_c2s.parseFrom(s);
			case -578678598:
				return org.gof.demo.worldsrv.msg.MsgRole.role_change_skin_s2c.parseFrom(s);
			case -625859105:
				return org.gof.demo.worldsrv.msg.MsgRole.role_choose_plan_c2s.parseFrom(s);
			case -625843745:
				return org.gof.demo.worldsrv.msg.MsgRole.role_choose_plan_s2c.parseFrom(s);
			case -1671993236:
				return org.gof.demo.worldsrv.msg.MsgRole.role_client_log_c2s.parseFrom(s);
			case -1914096305:
				return org.gof.demo.worldsrv.msg.MsgRole.role_common_speed_c2s.parseFrom(s);
			case 902461305:
				return org.gof.demo.worldsrv.msg.MsgRole.role_complaint_c2s.parseFrom(s);
			case -259017726:
				return org.gof.demo.worldsrv.msg.MsgRole.role_complaint_check_c2s.parseFrom(s);
			case -259002366:
				return org.gof.demo.worldsrv.msg.MsgRole.role_complaint_check_s2c.parseFrom(s);
			case 902476665:
				return org.gof.demo.worldsrv.msg.MsgRole.role_complaint_s2c.parseFrom(s);
			case -366513196:
				return org.gof.demo.worldsrv.msg.MsgRole.role_default_plan_info_c2s.parseFrom(s);
			case -366497836:
				return org.gof.demo.worldsrv.msg.MsgRole.role_default_plan_info_s2c.parseFrom(s);
			case 515356903:
				return org.gof.demo.worldsrv.msg.MsgRole.role_goods_refresh_list_c2s.parseFrom(s);
			case 515372263:
				return org.gof.demo.worldsrv.msg.MsgRole.role_goods_refresh_list_s2c.parseFrom(s);
			case 488767946:
				return org.gof.demo.worldsrv.msg.MsgRole.role_guide_c2s.parseFrom(s);
			case 488783306:
				return org.gof.demo.worldsrv.msg.MsgRole.role_guide_s2c.parseFrom(s);
			case 349942908:
				return org.gof.demo.worldsrv.msg.MsgRole.role_guide_save_c2s.parseFrom(s);
			case 349958268:
				return org.gof.demo.worldsrv.msg.MsgRole.role_guide_save_s2c.parseFrom(s);
			case 452655467:
				return org.gof.demo.worldsrv.msg.MsgRole.role_head_list_c2s.parseFrom(s);
			case 452670827:
				return org.gof.demo.worldsrv.msg.MsgRole.role_head_list_s2c.parseFrom(s);
			case 369445727:
				return org.gof.demo.worldsrv.msg.MsgRole.role_head_red_point_c2s.parseFrom(s);
			case 369461087:
				return org.gof.demo.worldsrv.msg.MsgRole.role_head_red_point_s2c.parseFrom(s);
			case -1955759478:
				return org.gof.demo.worldsrv.msg.MsgRole.role_info_c2s.parseFrom(s);
			case -406960657:
				return org.gof.demo.worldsrv.msg.MsgRole.role_info_change_c2s.parseFrom(s);
			case -406945297:
				return org.gof.demo.worldsrv.msg.MsgRole.role_info_change_s2c.parseFrom(s);
			case -1955744118:
				return org.gof.demo.worldsrv.msg.MsgRole.role_info_s2c.parseFrom(s);
			case 10539586:
				return org.gof.demo.worldsrv.msg.MsgRole.role_job_figure_c2s.parseFrom(s);
			case 1110145915:
				return org.gof.demo.worldsrv.msg.MsgRole.role_open_function_s2c.parseFrom(s);
			case 2037519615:
				return org.gof.demo.worldsrv.msg.MsgRole.role_others_c2s.parseFrom(s);
			case 2037534975:
				return org.gof.demo.worldsrv.msg.MsgRole.role_others_s2c.parseFrom(s);
			case -1683860214:
				return org.gof.demo.worldsrv.msg.MsgRole.role_phone_bind_c2s.parseFrom(s);
			case -1683844854:
				return org.gof.demo.worldsrv.msg.MsgRole.role_phone_bind_s2c.parseFrom(s);
			case -945109001:
				return org.gof.demo.worldsrv.msg.MsgRole.role_phone_captcha_c2s.parseFrom(s);
			case -945093641:
				return org.gof.demo.worldsrv.msg.MsgRole.role_phone_captcha_s2c.parseFrom(s);
			case 1849554171:
				return org.gof.demo.worldsrv.msg.MsgRole.role_phone_info_c2s.parseFrom(s);
			case 1849569531:
				return org.gof.demo.worldsrv.msg.MsgRole.role_phone_info_s2c.parseFrom(s);
			case 171133522:
				return org.gof.demo.worldsrv.msg.MsgRole.role_plan_info_c2s.parseFrom(s);
			case 171148882:
				return org.gof.demo.worldsrv.msg.MsgRole.role_plan_info_s2c.parseFrom(s);
			case 1561395012:
				return org.gof.demo.worldsrv.msg.MsgRole.role_power_info_s2c.parseFrom(s);
			case -482453342:
				return org.gof.demo.worldsrv.msg.MsgRole.role_preview_reward_c2s.parseFrom(s);
			case -482437982:
				return org.gof.demo.worldsrv.msg.MsgRole.role_preview_reward_s2c.parseFrom(s);
			case -1751225770:
				return org.gof.demo.worldsrv.msg.MsgRole.role_preview_s2c.parseFrom(s);
			case -736211288:
				return org.gof.demo.worldsrv.msg.MsgRole.role_quick_set_double_chapter_plan_c2s.parseFrom(s);
			case -736195928:
				return org.gof.demo.worldsrv.msg.MsgRole.role_quick_set_double_chapter_plan_s2c.parseFrom(s);
			case 1828636592:
				return org.gof.demo.worldsrv.msg.MsgRole.role_red_point_c2s.parseFrom(s);
			case 1996045833:
				return org.gof.demo.worldsrv.msg.MsgRole.role_red_point_change_s2c.parseFrom(s);
			case -1277403271:
				return org.gof.demo.worldsrv.msg.MsgRole.role_red_point_click_c2s.parseFrom(s);
			case 1828651952:
				return org.gof.demo.worldsrv.msg.MsgRole.role_red_point_s2c.parseFrom(s);
			case 950829914:
				return org.gof.demo.worldsrv.msg.MsgRole.role_rename_c2s.parseFrom(s);
			case 950845274:
				return org.gof.demo.worldsrv.msg.MsgRole.role_rename_s2c.parseFrom(s);
			case 696928783:
				return org.gof.demo.worldsrv.msg.MsgRole.role_resource_change_s2c.parseFrom(s);
			case 1082109953:
				return org.gof.demo.worldsrv.msg.MsgRole.role_set_setting_c2s.parseFrom(s);
			case 1082125313:
				return org.gof.demo.worldsrv.msg.MsgRole.role_set_setting_s2c.parseFrom(s);
			case -1121759079:
				return org.gof.demo.worldsrv.msg.MsgRole.role_setting_info_c2s.parseFrom(s);
			case -1121743719:
				return org.gof.demo.worldsrv.msg.MsgRole.role_setting_info_s2c.parseFrom(s);
			case 1000667794:
				return org.gof.demo.worldsrv.msg.MsgRole.role_seven_login_info_c2s.parseFrom(s);
			case 1000683154:
				return org.gof.demo.worldsrv.msg.MsgRole.role_seven_login_info_s2c.parseFrom(s);
			case 1751701715:
				return org.gof.demo.worldsrv.msg.MsgRole.role_seven_login_reward_c2s.parseFrom(s);
			case 1751717075:
				return org.gof.demo.worldsrv.msg.MsgRole.role_seven_login_reward_s2c.parseFrom(s);
			case 636159607:
				return org.gof.demo.worldsrv.msg.MsgRole.role_show_control_s2c.parseFrom(s);
			case -1161158034:
				return org.gof.demo.worldsrv.msg.MsgRole.role_skin_list_c2s.parseFrom(s);
			case -1161142674:
				return org.gof.demo.worldsrv.msg.MsgRole.role_skin_list_s2c.parseFrom(s);
			case 101169309:
				return org.gof.demo.worldsrv.msg.MsgRole.role_skin_upgrade_lv_c2s.parseFrom(s);
			case 101184669:
				return org.gof.demo.worldsrv.msg.MsgRole.role_skin_upgrade_lv_s2c.parseFrom(s);
			case -534998656:
				return org.gof.demo.worldsrv.msg.MsgRole.role_sp_cmp_c2s.parseFrom(s);
			case -534983296:
				return org.gof.demo.worldsrv.msg.MsgRole.role_sp_cmp_s2c.parseFrom(s);
			case -1769277277:
				return org.gof.demo.worldsrv.msg.MsgRole.role_total_sp_info_c2s.parseFrom(s);
			case -1769261917:
				return org.gof.demo.worldsrv.msg.MsgRole.role_total_sp_info_s2c.parseFrom(s);
			case 727080766:
				return org.gof.demo.worldsrv.msg.MsgRole.role_total_sp_update_s2c.parseFrom(s);
			case 664457126:
				return org.gof.demo.worldsrv.msg.MsgRole.role_unlock_skin_c2s.parseFrom(s);
			case 614091629:
				return org.gof.demo.worldsrv.msg.MsgRole.role_update_plan_c2s.parseFrom(s);
			case 614106989:
				return org.gof.demo.worldsrv.msg.MsgRole.role_update_plan_s2c.parseFrom(s);
			case 1321909562:
				return org.gof.demo.worldsrv.msg.MsgRole.role_used_skin_list_c2s.parseFrom(s);
			case 1321924922:
				return org.gof.demo.worldsrv.msg.MsgRole.role_used_skin_list_s2c.parseFrom(s);
			case 1170529171:
				return org.gof.demo.worldsrv.msg.MsgRole.unregister_c2s.parseFrom(s);
			case 1170544531:
				return org.gof.demo.worldsrv.msg.MsgRole.unregister_s2c.parseFrom(s);
			case -1393881193:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_act_info_c2s.parseFrom(s);
			case -1393865833:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_act_info_s2c.parseFrom(s);
			case 1254795353:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_action_point_item_c2s.parseFrom(s);
			case 1239984919:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_air_drop_info_s2c.parseFrom(s);
			case -1415616516:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_air_drop_tip_s2c.parseFrom(s);
			case 133843027:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_attack_info_c2s.parseFrom(s);
			case 133858387:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_attack_info_s2c.parseFrom(s);
			case 418644419:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_back_c2s.parseFrom(s);
			case -1452903212:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_battle_reports_c2s.parseFrom(s);
			case -1452887852:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_battle_reports_s2c.parseFrom(s);
			case -647982649:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_bounty_c2s.parseFrom(s);
			case -647967289:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_bounty_s2c.parseFrom(s);
			case 868078920:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_defend_detail_c2s.parseFrom(s);
			case 868094280:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_defend_detail_s2c.parseFrom(s);
			case -1527019611:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_defend_info_c2s.parseFrom(s);
			case -1527004251:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_defend_info_s2c.parseFrom(s);
			case 1332542228:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_defend_result_s2c.parseFrom(s);
			case 445208212:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_emoji_c2s.parseFrom(s);
			case 445223572:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_emoji_s2c.parseFrom(s);
			case -66162392:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_empire_occupy_c2s.parseFrom(s);
			case -66147032:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_empire_occupy_s2c.parseFrom(s);
			case 1623766404:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_empire_occupy_tip_s2c.parseFrom(s);
			case -2099231706:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_enter_c2s.parseFrom(s);
			case -2099216346:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_enter_s2c.parseFrom(s);
			case -654261394:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_grid_del_s2c.parseFrom(s);
			case 1724624709:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_grid_list_s2c.parseFrom(s);
			case -1750809579:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_hall_c2s.parseFrom(s);
			case -1750794219:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_hall_s2c.parseFrom(s);
			case 1704918254:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_locate_c2s.parseFrom(s);
			case 1704933614:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_locate_s2c.parseFrom(s);
			case 1768501777:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_location_c2s.parseFrom(s);
			case 1768517137:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_location_s2c.parseFrom(s);
			case 1264750672:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_mini_map_c2s.parseFrom(s);
			case 1264766032:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_mini_map_s2c.parseFrom(s);
			case 812080591:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_monster_info_c2s.parseFrom(s);
			case 812095951:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_monster_info_s2c.parseFrom(s);
			case -1355026754:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_monster_result_s2c.parseFrom(s);
			case -1806715987:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_move_c2s.parseFrom(s);
			case 1148958969:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_move_del_s2c.parseFrom(s);
			case 1789881114:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_move_list_s2c.parseFrom(s);
			case 2083145011:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_pvp_times_update_s2c.parseFrom(s);
			case 1541286699:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_quit_c2s.parseFrom(s);
			case 1541302059:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_quit_s2c.parseFrom(s);
			case -720280411:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_reconnect_c2s.parseFrom(s);
			case 1508241010:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_recover_c2s.parseFrom(s);
			case -365746823:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_relic_detail_c2s.parseFrom(s);
			case -365731463:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_relic_detail_s2c.parseFrom(s);
			case 98716485:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_relic_result_s2c.parseFrom(s);
			case -1258476175:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_revive_c2s.parseFrom(s);
			case 1858814539:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_reward_s2c.parseFrom(s);
			case -55198683:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_role_info_s2c.parseFrom(s);
			case -542885820:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_search_c2s.parseFrom(s);
			case -542870460:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_search_s2c.parseFrom(s);
			case -748935454:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_set_born_c2s.parseFrom(s);
			case -1315234518:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_share_info_c2s.parseFrom(s);
			case -1315219158:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_share_info_s2c.parseFrom(s);
			case -384422673:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_speed_up_c2s.parseFrom(s);
			case 594116830:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_spy_on_c2s.parseFrom(s);
			case 21980970:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_target_info_c2s.parseFrom(s);
			case 21996330:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_target_info_s2c.parseFrom(s);
			case 1831501675:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_target_reward_c2s.parseFrom(s);
			case 1831517035:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_target_reward_s2c.parseFrom(s);
			case -918344162:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_team_info_s2c.parseFrom(s);
			case 91736137:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_video_c2s.parseFrom(s);
			case 91751497:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_video_s2c.parseFrom(s);
			case -506451839:
				return org.gof.demo.worldsrv.msg.MsgS1.s1_view_c2s.parseFrom(s);
			case -651056615:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_battle_over_c2s.parseFrom(s);
			case -214090684:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_battle_s2c.parseFrom(s);
			case 971889355:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_enter_battle_s2c.parseFrom(s);
			case 1008811318:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_enter_s2c.parseFrom(s);
			case -766885483:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_leave_c2s.parseFrom(s);
			case -766870123:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_leave_s2c.parseFrom(s);
			case -726251726:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_load_s2c.parseFrom(s);
			case 1624290551:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_add_s2c.parseFrom(s);
			case -629785138:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_attack_cd_s2c.parseFrom(s);
			case 27851617:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_del_s2c.parseFrom(s);
			case -54791323:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_move_c2s.parseFrom(s);
			case -102497578:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_move_rocker_c2s.parseFrom(s);
			case -102482218:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_move_rocker_s2c.parseFrom(s);
			case -1939081483:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_move_rocker_stop_c2s.parseFrom(s);
			case -1939066123:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_move_rocker_stop_s2c.parseFrom(s);
			case -54775963:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_move_s2c.parseFrom(s);
			case 29703106:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_static_s2c.parseFrom(s);
			case 1438870171:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_update_elem_s2c.parseFrom(s);
			case -155909987:
				return org.gof.demo.worldsrv.msg.MsgScene.scene_obj_update_s2c.parseFrom(s);
			case -31836945:
				return org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.parseFrom(s);
			case 1039596874:
				return org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.parseFrom(s);
			case 1039612234:
				return org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.parseFrom(s);
			case 653600823:
				return org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.parseFrom(s);
			case -1479289307:
				return org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.parseFrom(s);
			case -752520617:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_draw_info_c2s.parseFrom(s);
			case -752505257:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_draw_info_s2c.parseFrom(s);
			case -1539890643:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_draw_lotto_c2s.parseFrom(s);
			case -1539875283:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_draw_lotto_s2c.parseFrom(s);
			case 491147358:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_book_update_s2c.parseFrom(s);
			case 1441093519:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_box_level_c2s.parseFrom(s);
			case 1441108879:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_box_level_s2c.parseFrom(s);
			case 2082131755:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_box_open_all_c2s.parseFrom(s);
			case 2082147115:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_box_open_all_s2c.parseFrom(s);
			case 1673787721:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_box_open_c2s.parseFrom(s);
			case 1673803081:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_box_open_s2c.parseFrom(s);
			case 1150970171:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_change_s2c.parseFrom(s);
			case -104357703:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_info_c2s.parseFrom(s);
			case -104342343:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_info_s2c.parseFrom(s);
			case -627592447:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_shop_c2s.parseFrom(s);
			case -627577087:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_shop_s2c.parseFrom(s);
			case -1012790518:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_equip_wear_c2s.parseFrom(s);
			case -663184424:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_favor_up_c2s.parseFrom(s);
			case -663169064:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_favor_up_s2c.parseFrom(s);
			case -1860333504:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_figure_c2s.parseFrom(s);
			case -1860318144:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_figure_s2c.parseFrom(s);
			case -1661348459:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_hang_up_box_info_c2s.parseFrom(s);
			case -1661333099:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_hang_up_box_info_s2c.parseFrom(s);
			case -940376298:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_hang_up_box_reward_c2s.parseFrom(s);
			case -869038390:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_info_c2s.parseFrom(s);
			case -869023030:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_info_s2c.parseFrom(s);
			case 237606418:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_level_s2c.parseFrom(s);
			case 1974262660:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_power_info_s2c.parseFrom(s);
			case 1625176224:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_room_info_c2s.parseFrom(s);
			case 1625191584:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_room_info_s2c.parseFrom(s);
			case -1378265093:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_room_update_c2s.parseFrom(s);
			case -1378249733:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_room_update_s2c.parseFrom(s);
			case -1654561506:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_skin_level_c2s.parseFrom(s);
			case -1654546146:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_skin_level_s2c.parseFrom(s);
			case -1992784286:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_station_up_c2s.parseFrom(s);
			case -1992768926:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_station_up_s2c.parseFrom(s);
			case 1479455843:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_total_sp_info_c2s.parseFrom(s);
			case 1479471203:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_total_sp_info_s2c.parseFrom(s);
			case 318384894:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_total_sp_update_s2c.parseFrom(s);
			case -876988038:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_transform_c2s.parseFrom(s);
			case -876972678:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_transform_s2c.parseFrom(s);
			case -740955956:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_treasure_info_c2s.parseFrom(s);
			case -740940596:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_treasure_info_s2c.parseFrom(s);
			case 1656722656:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_treasure_pos_c2s.parseFrom(s);
			case 1656738016:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_treasure_pos_s2c.parseFrom(s);
			case 651178499:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_treasure_uplv_c2s.parseFrom(s);
			case 651193859:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_treasure_uplv_s2c.parseFrom(s);
			case 1092772015:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_use_id_c2s.parseFrom(s);
			case 1092787375:
				return org.gof.demo.worldsrv.msg.MsgShip.ship_use_id_s2c.parseFrom(s);
			case -1204894060:
				return org.gof.demo.worldsrv.msg.MsgShop.shop_buy_c2s.parseFrom(s);
			case -1204878700:
				return org.gof.demo.worldsrv.msg.MsgShop.shop_buy_s2c.parseFrom(s);
			case -1829049718:
				return org.gof.demo.worldsrv.msg.MsgShop.shop_info_c2s.parseFrom(s);
			case -1829034358:
				return org.gof.demo.worldsrv.msg.MsgShop.shop_info_s2c.parseFrom(s);
			case -888609811:
				return org.gof.demo.worldsrv.msg.MsgShop.shop_limit_time_c2s.parseFrom(s);
			case -888594451:
				return org.gof.demo.worldsrv.msg.MsgShop.shop_limit_time_s2c.parseFrom(s);
			case 400292298:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.parseFrom(s);
			case 745844876:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.parseFrom(s);
			case 745860236:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.parseFrom(s);
			case 499964399:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.parseFrom(s);
			case 499979759:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.parseFrom(s);
			case -1703707176:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.parseFrom(s);
			case -683359648:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.parseFrom(s);
			case -683344288:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.parseFrom(s);
			case -1017250676:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.parseFrom(s);
			case 1172660451:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.parseFrom(s);
			case -465993336:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.parseFrom(s);
			case -465977976:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.parseFrom(s);
			case -666545882:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.parseFrom(s);
			case -666530522:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.parseFrom(s);
			case -404970751:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.parseFrom(s);
			case 698818906:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.parseFrom(s);
			case 698834266:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.parseFrom(s);
			case 1591837112:
				return org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.parseFrom(s);
			case 373157657:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.parseFrom(s);
			case 373173017:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.parseFrom(s);
			case 1780824528:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.parseFrom(s);
			case 1780839888:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.parseFrom(s);
			case 948098409:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.parseFrom(s);
			case 948113769:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.parseFrom(s);
			case -828897975:
				return org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.parseFrom(s);
			case -679835283:
				return org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.parseFrom(s);
			case -679819923:
				return org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.parseFrom(s);
			case 1780122658:
				return org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.parseFrom(s);
			case 1780138018:
				return org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.parseFrom(s);
			case -817485296:
				return org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.parseFrom(s);
			case 1193812765:
				return org.gof.demo.worldsrv.msg.MsgTask.task_achievement_c2s.parseFrom(s);
			case -1234975237:
				return org.gof.demo.worldsrv.msg.MsgTask.task_achievement_reward_c2s.parseFrom(s);
			case -1234959877:
				return org.gof.demo.worldsrv.msg.MsgTask.task_achievement_reward_s2c.parseFrom(s);
			case 1193828125:
				return org.gof.demo.worldsrv.msg.MsgTask.task_achievement_s2c.parseFrom(s);
			case 1555674798:
				return org.gof.demo.worldsrv.msg.MsgTask.task_action_finish_channel_c2s.parseFrom(s);
			case 1555690158:
				return org.gof.demo.worldsrv.msg.MsgTask.task_action_finish_channel_s2c.parseFrom(s);
			case 2017188239:
				return org.gof.demo.worldsrv.msg.MsgTask.task_all_s2c.parseFrom(s);
			case 1503007829:
				return org.gof.demo.worldsrv.msg.MsgTask.task_commit_all_c2s.parseFrom(s);
			case 1503023189:
				return org.gof.demo.worldsrv.msg.MsgTask.task_commit_all_s2c.parseFrom(s);
			case -1590402189:
				return org.gof.demo.worldsrv.msg.MsgTask.task_commit_c2s.parseFrom(s);
			case -1590386829:
				return org.gof.demo.worldsrv.msg.MsgTask.task_commit_s2c.parseFrom(s);
			case 1210571352:
				return org.gof.demo.worldsrv.msg.MsgTask.task_daily_point_s2c.parseFrom(s);
			case -840723759:
				return org.gof.demo.worldsrv.msg.MsgTask.task_fly_achievement_c2s.parseFrom(s);
			case -468646969:
				return org.gof.demo.worldsrv.msg.MsgTask.task_fly_achievement_reward_c2s.parseFrom(s);
			case -468631609:
				return org.gof.demo.worldsrv.msg.MsgTask.task_fly_achievement_reward_s2c.parseFrom(s);
			case -840708399:
				return org.gof.demo.worldsrv.msg.MsgTask.task_fly_achievement_s2c.parseFrom(s);
			case 1155039090:
				return org.gof.demo.worldsrv.msg.MsgTask.task_req_daily_box_c2s.parseFrom(s);
			case 1155054450:
				return org.gof.demo.worldsrv.msg.MsgTask.task_req_daily_box_s2c.parseFrom(s);
			case 1390384645:
				return org.gof.demo.worldsrv.msg.MsgTask.task_update_s2c.parseFrom(s);
			case 1697833372:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_apply_c2s.parseFrom(s);
			case 1697848732:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_apply_s2c.parseFrom(s);
			case 1938567451:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_choose_diff_c2s.parseFrom(s);
			case 1938582811:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_choose_diff_s2c.parseFrom(s);
			case -334454093:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_choose_pet_c2s.parseFrom(s);
			case -334438733:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_choose_pet_s2c.parseFrom(s);
			case 621308848:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_combat_c2s.parseFrom(s);
			case 621324208:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_combat_s2c.parseFrom(s);
			case -1921070664:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_create_c2s.parseFrom(s);
			case -1921055304:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_create_s2c.parseFrom(s);
			case 1324444314:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_exit_c2s.parseFrom(s);
			case 1324459674:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_exit_s2c.parseFrom(s);
			case -669856214:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_info_c2s.parseFrom(s);
			case -669840854:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_info_s2c.parseFrom(s);
			case 2045421734:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_join_c2s.parseFrom(s);
			case 2045437094:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_join_s2c.parseFrom(s);
			case 92550259:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_match_c2s.parseFrom(s);
			case 92565619:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_match_s2c.parseFrom(s);
			case -1611852463:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_ready_c2s.parseFrom(s);
			case -1611837103:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_ready_s2c.parseFrom(s);
			case 432830553:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_result_c2s.parseFrom(s);
			case 432845913:
				return org.gof.demo.worldsrv.msg.MsgTeam.team_result_s2c.parseFrom(s);
			case -793286814:
				return org.gof.demo.worldsrv.msg.MsgTitle.title_info_c2s.parseFrom(s);
			case -793271454:
				return org.gof.demo.worldsrv.msg.MsgTitle.title_info_s2c.parseFrom(s);
			case 899621336:
				return org.gof.demo.worldsrv.msg.MsgTitle.title_red_point_c2s.parseFrom(s);
			case 899636696:
				return org.gof.demo.worldsrv.msg.MsgTitle.title_red_point_s2c.parseFrom(s);
			case -1701719629:
				return org.gof.demo.worldsrv.msg.MsgTitle.title_wear_c2s.parseFrom(s);
			case -1701704269:
				return org.gof.demo.worldsrv.msg.MsgTitle.title_wear_s2c.parseFrom(s);
			case -1207737892:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_apply_c2s.parseFrom(s);
			case -632551861:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_apply_list_c2s.parseFrom(s);
			case -632536501:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_apply_list_s2c.parseFrom(s);
			case 1180242512:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_apply_opt_c2s.parseFrom(s);
			case 1180257872:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_apply_opt_s2c.parseFrom(s);
			case -1207722532:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_apply_s2c.parseFrom(s);
			case 1244178941:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_history_role_c2s.parseFrom(s);
			case 1244194301:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_history_role_s2c.parseFrom(s);
			case -1405605475:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_history_serv_c2s.parseFrom(s);
			case -1405590115:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_history_serv_s2c.parseFrom(s);
			case 621889002:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_info_c2s.parseFrom(s);
			case 621904362:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_info_s2c.parseFrom(s);
			case 565380345:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_invite_opt_c2s.parseFrom(s);
			case 565395705:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_invite_opt_s2c.parseFrom(s);
			case -336390926:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_log_c2s.parseFrom(s);
			case -336375566:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_log_s2c.parseFrom(s);
			case 642904329:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_role_status_c2s.parseFrom(s);
			case 642919689:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_role_status_s2c.parseFrom(s);
			case 2060754504:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_serv_detail_c2s.parseFrom(s);
			case 2060769864:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_serv_detail_s2c.parseFrom(s);
			case 1791648917:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_serv_list_c2s.parseFrom(s);
			case 1791664277:
				return org.gof.demo.worldsrv.msg.MsgTransfer.transfer_serv_list_s2c.parseFrom(s);
			case -663900218:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.parseFrom(s);
			case -663884858:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.parseFrom(s);
			case 810748316:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.parseFrom(s);
			case -1621955439:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.parseFrom(s);
			case -1621940079:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.parseFrom(s);
			case -1964238199:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.parseFrom(s);
			case -1255023001:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.parseFrom(s);
			case -1255007641:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.parseFrom(s);
			case 100008138:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.parseFrom(s);
			case 100023498:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.parseFrom(s);
			case 19117626:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.parseFrom(s);
			case 19132986:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.parseFrom(s);
			case -542824480:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.parseFrom(s);
			case 1883348300:
				return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.parseFrom(s);
			case -2035273547:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_collection_active_c2s.parseFrom(s);
			case -2035258187:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_collection_active_s2c.parseFrom(s);
			case 467177870:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_feather_level_up_c2s.parseFrom(s);
			case 467193230:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_feather_level_up_s2c.parseFrom(s);
			case -2115516054:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_info_c2s.parseFrom(s);
			case -2115500694:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_info_s2c.parseFrom(s);
			case 725417863:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_skill_use_c2s.parseFrom(s);
			case 725433223:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_skill_use_s2c.parseFrom(s);
			case -1125376346:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_skin_level_up_c2s.parseFrom(s);
			case -1125360986:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_skin_level_up_s2c.parseFrom(s);
			case 1465118413:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_change_tab_name_c2s.parseFrom(s);
			case 1465133773:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_change_tab_name_s2c.parseFrom(s);
			case 989722382:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_choose_tab_c2s.parseFrom(s);
			case 989737742:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_choose_tab_s2c.parseFrom(s);
			case -1737151586:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_lev_up_c2s.parseFrom(s);
			case -1737136226:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_lev_up_s2c.parseFrom(s);
			case 7555182:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_one_click_lev_up_c2s.parseFrom(s);
			case 7570542:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_one_click_lev_up_s2c.parseFrom(s);
			case 184710904:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_reset_c2s.parseFrom(s);
			case 184726264:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_talent_reset_s2c.parseFrom(s);
			case 55405109:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_use_c2s.parseFrom(s);
			case 55420469:
				return org.gof.demo.worldsrv.msg.MsgWing.wing_use_s2c.parseFrom(s);
			case -920404757:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_add_teammate_c2s.parseFrom(s);
			case -920389397:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_add_teammate_s2c.parseFrom(s);
			case 523846289:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_all_team_info_c2s.parseFrom(s);
			case 523861649:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_all_team_info_s2c.parseFrom(s);
			case 1417795369:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_feed_pet_c2s.parseFrom(s);
			case 1417810729:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_feed_pet_s2c.parseFrom(s);
			case 1375576732:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_remove_teammate_c2s.parseFrom(s);
			case 1375592092:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_remove_teammate_s2c.parseFrom(s);
			case 1347110520:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_remove_worker_c2s.parseFrom(s);
			case 1347125880:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_remove_worker_s2c.parseFrom(s);
			case 60587435:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_unlock_teammate_slot_c2s.parseFrom(s);
			case 348987372:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_unlock_teammate_slot_info_c2s.parseFrom(s);
			case 349002732:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_unlock_teammate_slot_info_s2c.parseFrom(s);
			case 60602795:
				return org.gof.demo.worldsrv.msg.MsgWorkerBase.worker_base_unlock_teammate_slot_s2c.parseFrom(s);
			case 1592506365:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.parseFrom(s);
			case 1592521725:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.parseFrom(s);
			case -1271246403:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.parseFrom(s);
			case -1271231043:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.parseFrom(s);
			case -1600445292:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.parseFrom(s);
			case 1249051184:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.parseFrom(s);
			case -966941049:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.parseFrom(s);
			case -966925689:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.parseFrom(s);
			case 1249066544:
				return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.parseFrom(s);
			case 356400101:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_add_materials_c2s.parseFrom(s);
			case 356415461:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_add_materials_s2c.parseFrom(s);
			case 682214927:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_add_worker_c2s.parseFrom(s);
			case 682230287:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_add_worker_s2c.parseFrom(s);
			case 286496177:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_auto_add_materials_c2s.parseFrom(s);
			case 286511537:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_auto_add_materials_s2c.parseFrom(s);
			case 79727629:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_cancel_work_c2s.parseFrom(s);
			case 79742989:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_cancel_work_s2c.parseFrom(s);
			case 129129437:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_choose_food_c2s.parseFrom(s);
			case 129144797:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_choose_food_s2c.parseFrom(s);
			case -955076362:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_crops_auto_transfer_c2s.parseFrom(s);
			case -955061002:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_crops_auto_transfer_s2c.parseFrom(s);
			case -777801350:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_crops_transfer_c2s.parseFrom(s);
			case -777785990:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_crops_transfer_s2c.parseFrom(s);
			case 223828950:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_dining_hall_c2s.parseFrom(s);
			case 223844310:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_dining_hall_s2c.parseFrom(s);
			case -1990929567:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_info_c2s.parseFrom(s);
			case -1990914207:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_info_s2c.parseFrom(s);
			case -1529054053:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_log_c2s.parseFrom(s);
			case -1529038693:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_log_s2c.parseFrom(s);
			case 738082878:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_make_fire_c2s.parseFrom(s);
			case 738098238:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_make_fire_s2c.parseFrom(s);
			case -1244409991:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_unlock_workshop_c2s.parseFrom(s);
			case -1244394631:
				return org.gof.demo.worldsrv.msg.MsgWorkerProcessing.worker_pw_unlock_workshop_s2c.parseFrom(s);
			}
		}catch (Exception e) {
			throw new SysException(e);
		}
		return null;
	}
	public static void init(){
		InputStream.setCreateMsgFunc(MsgSerializer::create);
	}
}
