package org.gof.demo;
import org.gof.core.support.observer.ObServer;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;

@GofGenFile
public final class MsgReceiverInit{
	public static <K,P> void init(ObServer<K, P> ob){
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_change_tab_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_change_tab_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_choose_tab_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_choose_tab_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_equip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_equip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_lv_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_lv_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_set_delay_time_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_set_delay_time_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_system_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_system_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSkill$skill_tab_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillMsgHandler.class))::_msg_skill_tab_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgLogin$login_auth_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParamAccount>)(ob.getTargetBean(org.gof.demo.seam.account.AccountMsgHandler.class))::_msg_login_auth_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgLogin$role_login_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParamAccount>)(ob.getTargetBean(org.gof.demo.seam.account.AccountMsgHandler.class))::_msg_role_login_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgLogin$role_reconnect_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParamAccount>)(ob.getTargetBean(org.gof.demo.seam.account.AccountMsgHandler.class))::onCSAccountReconnect, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge$accumulated_recharge_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.accumulatedRecharge.AccumulatedRechargeMsgHandler.class))::_msg_accumulated_recharge_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge$accumulated_recharge_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.accumulatedRecharge.AccumulatedRechargeMsgHandler.class))::_msg_accumulated_recharge_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_calendar_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_calendar_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_battle_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_battle_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_cook_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_cook_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_cultivate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_cultivate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_devour_stone_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_devour_stone_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_devour_stone_op_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_devour_stone_op_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_enter_scene_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_enter_scene_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_equip_upgrade_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_equip_upgrade_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_explore_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_explore_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_forge_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_forge_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_receive_ore_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_receive_ore_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_settle_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_settle_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_sweep_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_sweep_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_trigger_event_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_trigger_event_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_use_item_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::_msg_act_dungeon_use_item_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_accumulate_score_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_accumulate_score_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_airdrop_gift_del_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_airdrop_gift_del_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_airdrop_gift_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_airdrop_gift_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_airdrop_gift_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_airdrop_gift_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_airdrop_gift_open_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_airdrop_gift_open_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_angry_bird_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_angry_bird_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_angry_bird_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_angry_bird_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_angry_bird_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_angry_bird_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_angry_bird_shot_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_angry_bird_shot_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_guess_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_guess_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_guess_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_guess_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_pig_find_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_pig_find_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_autumn_pig_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_autumn_pig_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_bawang_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_bawang_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_bawang_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_bawang_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_box_tower_auto_next_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_box_tower_auto_next_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_box_tower_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_box_tower_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_box_tower_next_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_box_tower_next_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_box_tower_open_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_box_tower_open_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_box_tower_open_fifty_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_box_tower_open_fifty_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_box_tower_open_ten_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_box_tower_open_ten_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_break_gold_egg_break_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_break_gold_egg_break_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_break_gold_egg_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_break_gold_egg_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_break_gold_egg_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_break_gold_egg_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_break_gold_egg_next_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_break_gold_egg_next_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_broadcast_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_broadcast_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_camp_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_camp_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_camp_join_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_camp_join_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_camp_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_camp_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_camp_support_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_camp_support_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_card_ask_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_ask_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_buff_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_buff_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_card_chat_status_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_chat_status_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_eliminate_flip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_eliminate_flip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_eliminate_game_over_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_eliminate_game_over_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_eliminate_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_eliminate_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_eliminate_round_complete_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_eliminate_round_complete_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_eliminate_select_buff_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_eliminate_select_buff_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_card_eliminate_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_eliminate_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_card_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_card_recv_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_recv_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_card_search_role_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_search_role_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_card_send_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_card_send_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_castle_element_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_castle_element_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_castle_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_castle_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_castle_move_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_castle_move_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_castle_race_op_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_castle_race_op_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_castle_race_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_castle_race_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_castle_gamble_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_castle_refine_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_christmas_gift_gift_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_christmas_gift_gift_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_christmas_gift_open_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_christmas_gift_open_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_christmas_gift_role_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_christmas_gift_role_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_christmas_gift_send_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_christmas_gift_send_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_christmas_gift_share_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_christmas_gift_share_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_code_invite_bind_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_code_invite_bind_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_code_invite_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_code_invite_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_code_invite_share_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_code_invite_share_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cohesion_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cohesion_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cohesion_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cohesion_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_collect_blessing_ask_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_collect_blessing_ask_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_collect_blessing_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_collect_blessing_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_collect_blessing_recv_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_collect_blessing_recv_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_collect_blessing_send_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_collect_blessing_send_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_collect_five_blessing_luck_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_collect_five_blessing_luck_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_collect_five_blessing_show_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_collect_five_blessing_show_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cross_boss_buy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cross_boss_buy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cross_boss_claim_box_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cross_boss_claim_box_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cross_boss_claim_box_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cross_boss_claim_box_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cross_boss_claim_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cross_boss_claim_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cross_boss_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cross_boss_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_cross_boss_throw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_cross_boss_throw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_day_pay_choose_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_day_pay_choose_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_day_pay_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_day_pay_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_double_draw_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_double_draw_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_double_draw_count_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_double_draw_count_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_double_draw_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_double_draw_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_double_draw_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_double_draw_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_craft_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_dungeon_craft_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_dungeon_craft_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_dungeon_craft_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_frog_bird_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_frog_bird_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_frog_bird_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_frog_bird_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_frog_bird_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_frog_bird_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_frog_cat_buy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_frog_cat_buy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_frog_cat_claim_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_frog_cat_claim_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_frog_cat_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_frog_cat_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_fruit_merge_end_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_fruit_merge_end_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_fruit_merge_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_fruit_merge_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_fruit_merge_stamina_cost_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_fruit_merge_stamina_cost_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_fruit_merge_state_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_fruit_merge_state_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_fruit_merge_use_item_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_fruit_merge_use_item_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_gold_egg_count_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_gold_egg_count_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_golden_tower_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_golden_tower_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_golden_tower_count_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_golden_tower_count_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_golden_tower_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_golden_tower_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_golden_tower_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_golden_tower_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_group_gift_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_group_gift_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_group_gift_opt_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_group_gift_opt_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_group_gift_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_group_gift_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_guild_pay_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_guild_pay_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_guild_pay_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_guild_pay_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_buy_buff_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_buy_buff_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_buy_pass_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_buy_pass_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_choose_buff_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_choose_buff_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_combat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_combat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_arena_trick_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_arena_trick_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_bundle_data_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_bundle_data_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_buy_chat_status_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_buy_chat_status_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_buy_data_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_buy_data_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_buy_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_buy_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_buy_quit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_buy_quit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_buy_share_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_buy_share_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_group_buy_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_group_buy_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_match_3_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_match_3_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_halloween_match_3_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_halloween_match_3_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_idol_change_idol_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_idol_change_idol_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_idol_pick_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_idol_pick_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_idol_story_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_idol_story_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_idol_story_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_idol_story_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_jigsaw_ask_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_jigsaw_ask_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_jigsaw_chat_status_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_jigsaw_chat_status_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_jigsaw_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_jigsaw_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_jigsaw_recv_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_jigsaw_recv_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_jigsaw_search_role_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_jigsaw_search_role_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_jigsaw_send_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_jigsaw_send_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_buff_active_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_buff_active_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_buff_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_buff_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_collection_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_collection_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_energy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_energy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_invasion_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_invasion_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_legion_merge_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_legion_merge_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_login_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_login_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_login_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_login_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_lucky_cat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_lucky_cat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_lucky_cat_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_lucky_cat_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_lucky_cat_report_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_lucky_cat_report_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mini_game_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mini_game_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mini_game_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mini_game_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mini_game_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mini_game_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mining_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mining_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mining_inherit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mining_inherit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mining_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mining_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mining_settle_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mining_settle_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_monopoly_circle_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_monopoly_circle_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_monopoly_dice_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_monopoly_dice_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_monopoly_double_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_monopoly_double_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_monopoly_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_monopoly_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mount_carnival_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mount_carnival_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mount_carnival_count_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mount_carnival_count_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mount_carnival_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mount_carnival_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_mount_carnival_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_mount_carnival_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_newyear_dinner_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_newyear_dinner_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_newyear_dinner_enjoy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_newyear_dinner_enjoy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_newyear_dinner_make_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_newyear_dinner_make_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_pay_rebate_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_pay_rebate_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_pay_rebate_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_pay_rebate_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_pillow_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_pillow_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_pillow_take_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_pillow_take_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_pillow_throw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_pillow_throw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_refresh_gift_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_refresh_gift_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_refresh_gift_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_refresh_gift_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_refresh_gift_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_refresh_gift_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_reverse_war_chapter_pass_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_reverse_war_chapter_pass_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_reverse_war_get_day_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_reverse_war_get_day_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_reverse_war_get_time_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_reverse_war_get_time_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_reverse_war_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_reverse_war_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_reverse_war_monster_equip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_reverse_war_monster_equip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_reverse_war_monster_unlock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_reverse_war_monster_unlock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_server_puzzle_claim_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_server_puzzle_claim_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_server_puzzle_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_server_puzzle_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_server_puzzle_use_goods_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_server_puzzle_use_goods_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_seven_trial_angry_bird_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_seven_trial_angry_bird_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_seven_trial_angry_bird_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_seven_trial_angry_bird_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_seven_trial_claim_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_seven_trial_claim_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_seven_trial_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_seven_trial_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_share_game_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_share_game_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_share_game_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_share_game_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_skin_try_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_skin_try_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_skin_try_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_skin_try_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_enter_stage_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_enter_stage_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_finish_stage_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_finish_stage_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_mix_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_mix_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_mix_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_mix_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_skill_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_skill_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_skill_refine_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_skill_refine_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_stage_progress_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_stage_progress_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_sweep_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_sweep_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_talent_upgrade_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_talent_upgrade_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_slime_wheel_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_slime_wheel_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_sns_share_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_sns_share_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct2$act_stamina_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_stamina_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_battle_pass_claim_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_battle_pass_claim_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_discount_mall_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_discount_mall_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_discount_mall_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_discount_mall_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_dungeon_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_dungeon_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_hour_reward_claim_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_hour_reward_claim_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_equip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_equip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_equip_rebuild_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_equip_rebuild_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_job_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_job_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_pet_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_pet_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_pet_unlock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_pet_unlock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_skill_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_skill_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_tab_skill_unlock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_tab_skill_unlock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_strategy_total_sp_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_strategy_total_sp_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_ask_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_ask_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_ask_teammate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_ask_teammate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_battlepass_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_battlepass_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_deal_ask_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_deal_ask_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_flower_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_flower_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_flower_search_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_flower_search_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_gift_tips_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_gift_tips_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_give_flower_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_give_flower_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tanabata_search_teammate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tanabata_search_teammate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tang_mole_choose_hammer_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tang_mole_choose_hammer_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tang_mole_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tang_mole_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tang_mole_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tang_mole_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_tang_mole_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_tang_mole_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_task_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_task_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_task_update_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_task_update_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_valentine_role_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_valentine_role_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_war_token_add_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_war_token_add_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_war_token_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_war_token_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_war_token_lev_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_war_token_lev_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_war_token_task_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_war_token_task_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_week_card_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_week_card_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$act_week_card_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::act_week_card_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$questionnaire_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::questionnaire_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAct$questionnaire_take_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::questionnaire_take_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWarToken$war_token_add_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::war_token_add_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWarToken$war_token_buy_level_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::war_token_buy_lev_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWarToken$war_token_claim_special_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::war_token_claim_special_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWarToken$war_token_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::war_token_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWarToken$war_token_lev_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::war_token_lev_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWarToken$war_token_task_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityMsgHandler.class))::war_token_task_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_array_change_tab_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_array_change_tab_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_array_choose_tab_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_array_choose_tab_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_battle_rotate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_battle_rotate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_develop_rotate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_develop_rotate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_draw_exchang_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_draw_exchang_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_draw_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_draw_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_goods_exchange_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_goods_exchange_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_star_array_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_star_array_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAngel$angel_upgrade_star_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelMsgHandler.class))::angel_upgrade_star_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChatBubble$change_chat_bubble_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::change_chat_bubble_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChatBubble$chat_bubble_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::chat_bubble_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChatBubble$chat_bubble_red_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::chat_bubble_red_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHeadFrame$head_frame_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::head_frame_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHeadFrame$head_frame_red_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::head_frame_red_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHeadFrame$head_frame_wear_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::head_frame_wear_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTitle$title_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::title_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTitle$title_red_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::title_red_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTitle$title_wear_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceMsgHandler.class))::title_wear_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_buy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_buy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_combat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_combat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_cross_sync_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_cross_sync_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_rank_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_rank_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_role_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_role_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArena$arena_video_play_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_arena_video_play_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_combat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_combat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_rank_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_rank_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_sign_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_sign_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossPvp$cross_pvp_video_play_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaMsgHandler.class))::_msg_cross_pvp_video_play_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifactGem$artifact_gem_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactGemMsgHandler.class))::artifact_gem_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifactGem$artifact_gem_lock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactGemMsgHandler.class))::artifact_gem_lock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifactGem$artifact_gem_red_read_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactGemMsgHandler.class))::artifact_gem_red_read_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifactGem$artifact_gem_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactGemMsgHandler.class))::artifact_gem_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifactGem$artifact_gem_wear_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactGemMsgHandler.class))::artifact_gem_wear_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifact$artifact_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactMsgHandler.class))::artifact_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifact$artifact_levup_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactMsgHandler.class))::artifact_levup_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifact$artifact_up_skin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactMsgHandler.class))::artifact_up_skin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifact$artifact_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactMsgHandler.class))::artifact_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgArtifact$artifact_use_skill_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactMsgHandler.class))::artifact_use_skill_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgReturn$return_checkin_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.back.BackMsgHandler.class))::_msg_return_checkin_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgReturn$return_checkin_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.back.BackMsgHandler.class))::_msg_return_checkin_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgReturn$return_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.back.BackMsgHandler.class))::_msg_return_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgReturn$return_task_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.back.BackMsgHandler.class))::_msg_return_task_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgReturn$return_task_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.back.BackMsgHandler.class))::_msg_return_task_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgBackLamp$back_lamp_chapter_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.backLamp.BackLampMsgHandler.class))::_msg_back_lamp_chapter_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgBackLamp$back_lamp_chapter_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.backLamp.BackLampMsgHandler.class))::_msg_back_lamp_chapter_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgBackLamp$back_lamp_get_acc_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.backLamp.BackLampMsgHandler.class))::_msg_back_lamp_get_acc_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgBackLamp$back_lamp_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.backLamp.BackLampMsgHandler.class))::_msg_back_lamp_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_combat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_combat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_effect_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_effect_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_free_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_free_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_look_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_look_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_rename_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_rename_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_reward_get_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_reward_get_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_search_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_search_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_video_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_video_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCaptureSlave$capture_slave_video_play_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveMsgHandler.class))::capture_slave_video_play_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_car_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_car_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_car_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_car_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_collect_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_collect_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_combat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_combat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_look_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_look_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_mount_id_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_mount_id_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_parking_help_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_parking_help_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_parking_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_parking_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_parking_stop_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_parking_stop_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_protect_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_protect_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_read_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_read_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_rename_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_rename_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_report_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_report_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_search_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_search_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_skin_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_skin_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_skin_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_skin_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_space_update_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_space_update_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$car_park_video_play_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::car_park_video_play_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_battle_queue_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_battle_queue_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_look_fighting_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_look_fighting_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_next_time_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_next_time_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_parking_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_parking_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_parking_stop_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_parking_stop_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_preview_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_preview_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_queue_join_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_queue_join_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_queue_stick_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_queue_stick_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_rank_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_rank_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_report_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_report_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_role_state_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_role_state_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCarPark$cross_car_park_video_play_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkMsgHandler.class))::cross_car_park_video_play_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_claim_level_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_claim_level_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_collection_room_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_collection_room_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_collection_room_like_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_collection_room_like_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_like_collection_room_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_like_collection_room_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_save_display_area_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_save_display_area_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_save_show_medal_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_save_show_medal_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCharm$charm_target_charm_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmMsgHandler.class))::_msg_charm_target_charm_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_choose_job_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_choose_job_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_get_idle_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_get_idle_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_idle_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_idle_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_is_holy_open_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_is_holy_open_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_kill_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_kill_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_reconnect_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_reconnect_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_revive_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_revive_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_enter_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_enter_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_select_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_select_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_speed_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_speed_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_transfer_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_transfer_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_transfer_close_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_transfer_close_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCrossWar$cross_war_scene_transfer_slide_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_cross_war_scene_transfer_slide_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgScene$scene_battle_over_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_scene_battle_over_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgScene$scene_leave_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_scene_leave_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgScene$scene_obj_move_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarMsgHandler.class))::_msg_scene_obj_move_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_change_optional_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_change_optional_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_abandon_teammate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_abandon_teammate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_agree_invite_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_agree_invite_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_cancel_help_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_cancel_help_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_change_invite_setting_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_change_invite_setting_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_change_pos_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_change_pos_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_change_strategy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_change_strategy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_get_help_manage_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_get_help_manage_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_get_invite_help_setting_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_get_invite_help_setting_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_get_level_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_get_level_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_get_teammate_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_get_teammate_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_get_teammate_skill_delay_time_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_get_teammate_skill_delay_time_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_get_through_level_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_get_through_level_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_is_in_invite_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_is_in_invite_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_refuse_invite_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_refuse_invite_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_restart_chapter_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_restart_chapter_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_restart_level_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_restart_level_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_set_teammate_skill_delay_time_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_set_teammate_skill_delay_time_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_single_teammate_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_single_teammate_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDoubleChapter$double_chapter_use_teammate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::double_chapter_use_teammate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_dc_entrance_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::dungeon_dc_entrance_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_dc_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterMsgHandler.class))::dungeon_dc_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_book_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_book_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_box_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_box_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_box_lv_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_box_lv_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_box_open_all_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_box_open_all_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_box_open_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_box_open_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_box_skin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_box_skin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_box_skin_unlock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_box_skin_unlock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_change_box_skin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_change_box_skin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_change_tab_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_change_tab_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_choose_tab_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_choose_tab_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_figure_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_figure_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_figure_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_figure_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_filter_attr_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_filter_attr_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_filter_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_filter_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_select_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_select_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_shop_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_shop_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_tab_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_tab_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgEquip$equip_wear_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipMsgHandler.class))::equip_wear_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_dismantle_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_dismantle_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_fusion_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_fusion_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_fusion_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_fusion_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_fusion_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_fusion_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_inlay_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_inlay_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_pray_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_pray_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_pray_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_pray_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_pray_set_auto_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_pray_set_auto_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_red_read_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_red_read_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_reset_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_reset_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFate$fate_show_choose_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateMsgHandler.class))::fate_show_choose_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_egg_incubate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_egg_incubate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_egg_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_egg_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_base_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_base_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_change_base_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_change_base_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_get_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_get_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_kick_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_kick_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_partner_shelves_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_partner_shelves_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_pet_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_pet_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_resp_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_resp_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_save_setting_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_save_setting_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_set_shelves_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_set_shelves_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_shelves_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_shelves_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_hybrid_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_hybrid_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_advance_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_advance_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_collection_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_collection_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_fight_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_fight_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_reborn_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_reborn_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_rename_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_rename_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_reset_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_reset_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_resolve_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_resolve_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_resolve_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_resolve_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFly$fly_pet_star_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetMsgHandler.class))::fly_pet_star_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSolo$solo_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::_msg_solo_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSolo$solo_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::_msg_solo_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_claim_gift_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_claim_gift_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_deal_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_deal_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_gift_num_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_gift_num_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_give_and_claim_gift_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_give_and_claim_gift_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_give_gift_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_give_gift_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_online_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_online_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_refresh_recommend_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_refresh_recommend_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_req_deal_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_req_deal_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFriend$friend_search_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendMsgHandler.class))::friend_search_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFund$fund_get_reward_all_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fund.FundMsgHandler.class))::fund_get_reward_all_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFund$fund_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fund.FundMsgHandler.class))::fund_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFund$fund_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fund.FundMsgHandler.class))::fund_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgFund$fund_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.fund.FundMsgHandler.class))::fund_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_league_solo_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_dungeon_league_solo_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_league_solo_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_dungeon_league_solo_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_apply_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_apply_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_apply_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_apply_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_approve_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_approve_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_area_enter_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_area_enter_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_area_exit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_area_exit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_area_move_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_area_move_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuildBoss$guild_boss_enter_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_boss_enter_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuildBoss$guild_boss_exit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_boss_exit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuildBoss$guild_boss_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_boss_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuildBoss$guild_boss_sign_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_boss_sign_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_change_career_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_change_career_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_create_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_create_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_dice_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_dice_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_dice_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_dice_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_dissolve_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_dissolve_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_donate_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_donate_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_help_ask_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_help_ask_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_help_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_help_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_help_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_help_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_help_status_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_help_status_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_info_update_s2c", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_info_update_s2c, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_join_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_join_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_kick_out_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_kick_out_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_log_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_log_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_members_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_members_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_question_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_question_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_quick_join_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_quick_join_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_quit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_quit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_rank_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_rank_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_rank_my_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_rank_my_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_schedule_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_schedule_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_search_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_search_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_setting_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_setting_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_treasure_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_treasure_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGuild$guild_treasure_open_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_guild_treasure_open_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_cancel_red_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_cancel_red_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_fight_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_fight_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_fight_report_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_fight_report_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_hall_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_hall_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_hall_of_fame_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_hall_of_fame_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_play_video_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_play_video_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_rank_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_rank_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_rank_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_rank_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_road_change_all_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_road_change_all_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_road_change_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_road_change_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_road_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_road_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_season_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_season_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_select_road_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_select_road_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_serv_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_serv_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGvg$gvg_week_rank_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildMsgHandler.class))::_msg_gvg_week_rank_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_auto_settle_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_auto_settle_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_cast_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_cast_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_daily_task_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_daily_task_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_data_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_data_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_fin_auto_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_fin_auto_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_get_album_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_get_album_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_ground_unlock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_ground_unlock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_group_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_group_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_house_design_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_house_design_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_house_equip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_house_equip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_house_equip_one_click_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_house_equip_one_click_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_house_set_use_design_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_house_set_use_design_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_reel_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_reel_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_start_auto_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_start_auto_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_tool_lv_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_tool_lv_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_fish_unlock_house_slot_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishMsgHandler.class))::home_fish_unlock_house_slot_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_battle_begin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_battle_begin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_battle_report_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_battle_report_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_battle_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_battle_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_building_lev_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_building_lev_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_fertilize_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_fertilize_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_harvest_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_harvest_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_human_check_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_human_check_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_human_check_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_human_check_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_log_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_log_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_pick_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_pick_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_plant_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_plant_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_play_video_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_play_video_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_search_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_search_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_search_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_search_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_spend_fruit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_spend_fruit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_statue_attr_lock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_statue_attr_lock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_statue_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_statue_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_statue_refresh_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_statue_refresh_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_statue_tab_change_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_statue_tab_change_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_farm_statue_tab_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_farm_statue_tab_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_mine_auto_use_goods_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_mine_auto_use_goods_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_mine_get_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_mine_get_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_mine_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_mine_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgHome$home_mine_use_goods_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::home_mine_use_goods_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgScience$science_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::science_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgScience$science_research_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeMsgHandler.class))::science_research_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAd$ad_wheel_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_ad_wheel_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAd$ad_wheel_spin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_ad_wheel_spin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgCollection$collection_enhance_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_collection_enhance_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgLogin$heart_beat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_heart_beat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_role_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSystem$server_merge_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_server_merge_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSolo$solo_video_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::_msg_solo_video_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAdventureTitle$adventure_title_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::adventure_title_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAdventureTitle$adventure_title_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::adventure_title_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$client_data_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::client_data_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$client_log_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::client_log_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_appeal_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_appeal_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_default_plan_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_default_plan_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_gender_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_gender_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_head_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_head_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_job_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_job_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_plan_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_plan_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_req_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_req_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_change_skin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_change_skin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_choose_plan_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_choose_plan_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_client_log_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_client_log_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_common_speed_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_common_speed_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_complaint_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_complaint_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_complaint_check_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_complaint_check_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_default_plan_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_default_plan_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_goods_refresh_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_goods_refresh_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_guide_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_guide_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_guide_save_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_guide_save_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_head_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_head_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_head_red_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_head_red_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_info_change_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_info_change_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_job_figure_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_job_figure_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_others_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_others_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_plan_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_plan_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_preview_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_preview_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_quick_set_double_chapter_plan_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_quick_set_double_chapter_plan_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_red_point_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_red_point_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_rename_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_rename_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_set_setting_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_set_setting_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_set_setting_s2c", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_set_setting_s2c, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_setting_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_setting_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_seven_login_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_seven_login_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_seven_login_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_seven_login_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_skin_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_skin_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_skin_upgrade_lv_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_skin_upgrade_lv_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_sp_cmp_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_sp_cmp_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_total_sp_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_total_sp_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_unlock_skin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_unlock_skin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_update_plan_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_update_plan_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$role_used_skin_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::role_used_skin_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRole$unregister_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanMsgHandler.class))::unregister_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChat$ban_chat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_ban_chat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChat$chat_channel_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_chat_channel_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChat$chat_friend_info_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_chat_friend_info_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChat$chat_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_chat_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChat$chat_history_read_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_chat_history_read_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgChat$chat_message_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_chat_message_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgSystem$system_gm_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.inform.InformMsgHandler.class))::_msg_system_gm_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_battle_more_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_battle_more_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_battle_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_battle_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_battle_start_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_battle_start_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_fate_daily_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_fate_daily_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_mount_battle_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_mount_battle_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_type_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_type_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_wing_quick_pass_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_wing_quick_pass_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_wing_reset_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_wing_reset_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_world_boss_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_dungeon_world_boss_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMainChapter$main_chapter_claim_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_main_chapter_claim_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMainChapter$main_chapter_enter_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_main_chapter_enter_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMainChapter$main_chapter_kill_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_main_chapter_kill_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMainChapter$main_chapter_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_main_chapter_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMainChapter$main_chapter_reward_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceMsgHandler.class))::_msg_main_chapter_reward_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGoods$goods_compose_skill_pet_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.item.ItemBagMsgHandler.class))::_msg_goods_compose_skill_pet_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgGoods$goods_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.item.ItemBagMsgHandler.class))::_msg_goods_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgJobsWakeup$jobs_wakeup_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.jobs.JobsMsgHandler.class))::_msg_jobs_wakeup_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgJobsWakeup$jobs_wakeup_item_transform_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.jobs.JobsMsgHandler.class))::_msg_jobs_wakeup_item_transform_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgJobsWakeup$jobs_wakeup_upgrade_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.jobs.JobsMsgHandler.class))::_msg_jobs_wakeup_upgrade_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgJobsWakeup$jobs_wakeup_wakeup_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.jobs.JobsMsgHandler.class))::_msg_jobs_wakeup_wakeup_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_bet_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_bet_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_bet_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_bet_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_champion_history_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_champion_history_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_champion_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_champion_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_enemys_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_enemys_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_knockout_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_knockout_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_lookup_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_lookup_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_lookup_role_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_lookup_role_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_loop_bet_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_loop_bet_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_loop_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_loop_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_loop_rank_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_loop_rank_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_null_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_null_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_pet_plan_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_pet_plan_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_play_video_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_play_video_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_play_video_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_play_video_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_pos_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_pos_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_rank_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_rank_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_report_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_report_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_req_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_req_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_req_team_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_req_team_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_resp_role_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_resp_role_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_team_combat_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_team_combat_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_team_create_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_team_create_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_team_exit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_team_exit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_team_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_team_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_team_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_team_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgKungfuRace$kungfu_race_worship_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceMsgHandler.class))::_msg_kungfu_race_worship_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMail$mail_claim_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mail.MailMsgHandler.class))::_msg_mail_claim_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMail$mail_delete_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mail.MailMsgHandler.class))::_msg_mail_delete_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMail$mail_expired_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mail.MailMsgHandler.class))::_msg_mail_expired_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMail$mail_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mail.MailMsgHandler.class))::_msg_mail_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMail$mail_read_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mail.MailMsgHandler.class))::_msg_mail_read_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$fake_recharge_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_fake_recharge_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_gift_draw_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_gift_draw_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_mall_check_push_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_mall_check_push_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_mall_custom_mall_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_mall_custom_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_mall_custom_mall_set_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_mall_custom_set_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_mall_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_mall_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_mall_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_mall_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$pay_mall_rewards_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_pay_mall_rewards_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPayMall$role_pay_check_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallPayMsgHandler.class))::_msg_role_pay_check_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDraw$draw_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallShopMsgHandler.class))::_msg_draw_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgShop$shop_buy_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallShopMsgHandler.class))::_msg_shop_buy_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgShop$shop_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallShopMsgHandler.class))::_msg_shop_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgShop$shop_limit_time_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallShopMsgHandler.class))::_msg_shop_limit_time_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMount$mount_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountMsgHandler.class))::mount_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMount$mount_levup_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountMsgHandler.class))::mount_levup_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMount$mount_talent_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountMsgHandler.class))::mount_talent_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMount$mount_up_skin_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountMsgHandler.class))::mount_up_skin_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMount$mount_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountMsgHandler.class))::mount_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgMount$mount_use_skill_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountMsgHandler.class))::mount_use_skill_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDraw$draw_card_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_draw_card_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_change_tab_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_change_tab_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_choose_tab_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_choose_tab_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_levup_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_levup_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_set_all_pos_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_set_all_pos_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_set_pos_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_set_pos_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_skin_equip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_skin_equip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_skin_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_skin_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPet$pet_tab_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetMsgHandler.class))::_msg_pet_tab_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgAd$ad_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeMsgHandler.class))::ad_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPrivilege$privilege_card_get_all_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeMsgHandler.class))::privilege_card_get_all_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPrivilege$privilege_card_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeMsgHandler.class))::privilege_card_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPrivilege$privilege_card_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeMsgHandler.class))::privilege_card_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgPrivilege$privilege_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeMsgHandler.class))::privilege_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRank$rank_like_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.rank.RankMsgHandler.class))::_msg_rank_like_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRank$rank_like_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.rank.RankMsgHandler.class))::_msg_rank_like_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRank$rank_cross_status_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.rank.RankMsgHandler.class))::rank_cross_status_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRank$rank_data_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.rank.RankMsgHandler.class))::rank_data_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRank$rank_serv_list_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.rank.RankMsgHandler.class))::rank_serv_list_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_change_tab_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_change_tab_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_change_tab_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_change_tab_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_choose_tab_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_choose_tab_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_equip_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_equip_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_find_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_find_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_tab_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_tab_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgRelic$relic_unlock_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicMsgHandler.class))::relic_unlock_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_achievement_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_achievement_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_achievement_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_achievement_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_action_finish_channel_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_action_finish_channel_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_commit_all_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_commit_all_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_commit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_commit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_fly_achievement_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_fly_achievement_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_fly_achievement_reward_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_fly_achievement_reward_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTask$task_req_daily_box_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskMsgHandler.class))::_msg_task_req_daily_box_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgDungeon$dungeon_sweep_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_dungeon_sweep_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_apply_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_apply_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_choose_diff_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_choose_diff_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_choose_pet_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_choose_pet_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_create_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_create_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_exit_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_exit_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_join_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_join_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_match_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_match_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_ready_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_ready_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgTeam$team_result_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamMsgHandler.class))::_msg_team_result_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_collection_active_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_collection_active_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_feather_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_feather_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_info_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_info_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_skill_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_skill_use_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_skin_level_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_skin_level_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_talent_change_tab_name_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_talent_change_tab_name_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_talent_choose_tab_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_talent_choose_tab_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_talent_lev_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_talent_lev_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_talent_one_click_lev_up_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_talent_one_click_lev_up_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_talent_reset_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_talent_reset_c2s, 1);  
		ob.reg("class org.gof.demo.worldsrv.msg.MsgWing$wing_use_c2s", (GofFunction1<org.gof.demo.seam.msg.MsgParam>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingMsgHandler.class))::wing_use_c2s, 1);  
	}
}

