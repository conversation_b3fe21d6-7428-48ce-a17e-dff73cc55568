package org.gof.demo.worldsrv.entity;


import co.paralleluniverse.fibers.Suspendable;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.gof.core.*;
import org.gof.core.db.DBConsts;
import org.gof.core.dbsrv.DB;
import org.gof.core.support.BufferPool;
import org.gof.core.support.S;
import org.gof.core.support.SysException;
import org.gof.core.support.log.LogCore;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import io.vertx.core.json.JsonObject;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@GofGenFile
public final class HomeFish extends EntityBase {
	public static final String tableName = "home_fish";
	public static final boolean autoCache = false;
	public static final String LISTKEY = "";

	public static final int REDIS_EXPIRE_TIME = 0;// redis过期时间

	public static final int UPDATE_DB_TYPE = 0;// 数据入库类型 0队列入库 1实时入库 2不入库

	public static long redisSyncTimeInterval = 0L;

	
	
	
	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String id = "id";	//id
		public static final String fisherLv = "fisherLv";	//钓鱼佬等级
		public static final String fisherExp = "fisherExp";	//钓鱼佬经验
		public static final String albumLv = "albumLv";	//图鉴等级
		public static final String albumExp = "albumExp";	//图鉴经验 (所有鱼类等级之和)
		public static final String totalScore = "totalScore";	//图鉴总分
		public static final String albumDetailMap = "albumDetailMap";	//图鉴详情JSONArray K:fishGroupSn, V:fishSn,len,lv,exp的String
		public static final String fishTools = "fishTools";	//渔具类型等级 K:toolType, V:level 的JSON
		public static final String houseList = "houseList";	//鱼塘槽位 K:locationId, V:fishGroupSn 的JSON
		public static final String albumScoreRewards = "albumScoreRewards";	//已领取的图鉴评分奖励ID列表
		public static final String houseDesign = "houseDesign";	//鱼塘装饰
		public static final String currentHouseDesign = "currentHouseDesign";	//当前鱼塘装饰
		public static final String dailyTaskGroupSn = "dailyTaskGroupSn";	//当前每日任务组SN
		public static final String dailyTasks = "dailyTasks";	//每日任务
		public static final String dailyTaskRecv = "dailyTaskRecv";	//是否已领取每日任务最终奖励
		public static final String unlockedMaxGround = "unlockedMaxGround";	//已解锁的最高常驻渔场ID
		public static final String fishGroundTasks = "fishGroundTasks";	//渔场解锁任务
		public static final String totalFishCount = "totalFishCount";	//总钓鱼次数
		public static final String dailyReelSuccCount = "dailyReelSuccCount";	//每日上钩次数
		public static final String autoFishGroundSn = "autoFishGroundSn";	//自动钓鱼所在的渔场SN (0代表未开启)
		public static final String autoFishStartTime = "autoFishStartTime";	//自动钓鱼开始时间戳
		public static final String autoFishBaitsSnNum = "autoFishBaitsSnNum";	//自动钓鱼选择的鱼饵和数量列表 [[baitSn, num], ...] 的JSON
		public static final String autoFishCastNum = "autoFishCastNum";	//自动钓鱼每次几钓 (1或10)
		public static final String autoFishSettleCount = "autoFishSettleCount";	//自动钓鱼已经结算的总次数
		public static final String firstFishMap = "firstFishMap";	//地图抛竿吸引到的第一只鱼
		public static final String totalAlbumFishes = "totalAlbumFishes";	//会话中破纪录的鱼JSONArray K:fishGroupSn, V:fishSn,len,lv,exp的String
		public static final String totalSellFishes = "totalSellFishes";	//会话中卖出的鱼 K:fishGroupSn, V:count 的JSON
		public static final String totalCastCnt = "totalCastCnt";	//会话中总抛竿次数
		public static final String totalReelSuccCnt = "totalReelSuccCnt";	//会话中总上钩次数
		public static final String totalSlippedCnt = "totalSlippedCnt";	//会话中总逃跑次数
		public static final String totalRewards = "totalRewards";	//会话中获得的总奖励 K:currencyId, V:amount 的JSON
		public static final String extendJSON = "extendJSON";	//扩展属性(用于处理bug)
	}

	@Override
	public String getTableName() {
		return tableName;
	}

	@Override
	public boolean isAutoCache(){
        return autoCache;
    }
	
	public HomeFish() {
		super();
		setFisherLv(1);
		setFisherExp(0);
		setAlbumLv(0);
		setAlbumExp(0);
		setTotalScore(0);
		setAlbumDetailMap("[]");
		setFishTools("{}");
		setHouseList("{}");
		setAlbumScoreRewards("");
		setHouseDesign("{}");
		setCurrentHouseDesign(0);
		setDailyTaskGroupSn(0);
		setDailyTasks("{}");
		setDailyTaskRecv(false);
		setUnlockedMaxGround(1);
		setFishGroundTasks("{}");
		setTotalFishCount(0);
		setDailyReelSuccCount(0);
		setAutoFishGroundSn(0);
		setAutoFishStartTime(0);
		setAutoFishBaitsSnNum("[]");
		setAutoFishCastNum(0);
		setAutoFishSettleCount(0);
		setFirstFishMap("{}");
		setTotalAlbumFishes("[]");
		setTotalSellFishes("{}");
		setTotalCastCnt(0);
		setTotalReelSuccCnt(0);
		setTotalSlippedCnt(0);
		setTotalRewards("{}");
		setExtendJSON("");
	}

	public HomeFish(Record record) {
		super(record);
	}

	
	/**
	 * 新增数据
	 */
	@Override
	public void persist() {
		
		if(getId() == 0){
			setTableId();
		}
		insertNew();
		
		//状态错误
		if(record.getStatus() != DBConsts.RECORD_STATUS_NEW) {
			LogCore.db.error("只有新增包能调用persist函数，请确认状态：data={}, stackTrace={}", this, ExceptionUtils.getStackTrace(new Throwable()));
			return;
		}
		
		DB prx = DB.newInstance(getTableName());
		prx.insert(record);
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 同步修改数据至DB服务器
	 * 默认不立即持久化到数据库
	 */
	@Override
	public void update() {
		update(false);
	}

	@Override
	public void updateRedis(boolean sync) {
		if(redisSyncTimeInterval == 0L){
			redisSyncTimeInterval = Port.getTime();
		}
		if(sync || (getUpdateObj() != null && Port.getTime() - redisSyncTimeInterval > 5 * 1000L)){// 避免关服瞬间所有玩家一次性写入太多
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
	}

	@Override
	public void updateDB(boolean sync) {

		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}

		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);

		//回收缓冲包
		patch.release();

		//重置状态
		record.resetStatus();
	}
	
	/**
	 * 同步修改数据至DB服务器
	 * @param sync 是否立即同持久化到数据库
	 */
	@Override
	public void update(boolean sync) {
		
		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}
		
		if(getUpdateObj() != null){
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
		
		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);
		
		//回收缓冲包
		patch.release();
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 删除数据
	 */
	@Override
	public void remove() {
		
		deleteNew();
	
		DB prx = DB.newInstance(getTableName());
		prx.delete(getId());
	}

	public void reset() {
		super.reset();
		record.setNewness(false);
		record.resetStatus();
	}

	protected String getKey() {
		return "HomeFish." + getId();
	}

	

	protected String getListKey() {
		return null;
	}
	
	protected String getListItemKey() {
		return null;
	}

	

	public void doCreate() {
		setTableId();
		insertNew();
	}

	public void setTableId() {
		setId(incrTableId("home_fish"));
	}

	public JsonObject insertNew() {
		return super.insert(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	public void deleteNew() {
		super.delete(getKey(), getId(), UPDATE_DB_TYPE, 0, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	/**
	* 根据入库类型更新
	*/
	public JsonObject updateNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	/**
	* 实时入库
	*/
	public JsonObject updateNowNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE_NOW, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	public JsonObject getAllObjNew(){
		JsonObject obj = super.getAllObjNew();
		obj.put(K.id, getId()); // id
		obj.put(K.fisherLv, getFisherLv()); // fisherLv
		obj.put(K.fisherExp, getFisherExp()); // fisherExp
		obj.put(K.albumLv, getAlbumLv()); // albumLv
		obj.put(K.albumExp, getAlbumExp()); // albumExp
		obj.put(K.totalScore, getTotalScore()); // totalScore
		obj.put(K.albumDetailMap, getAlbumDetailMap()); // albumDetailMap
		obj.put(K.fishTools, getFishTools()); // fishTools
		obj.put(K.houseList, getHouseList()); // houseList
		obj.put(K.albumScoreRewards, getAlbumScoreRewards()); // albumScoreRewards
		obj.put(K.houseDesign, getHouseDesign()); // houseDesign
		obj.put(K.currentHouseDesign, getCurrentHouseDesign()); // currentHouseDesign
		obj.put(K.dailyTaskGroupSn, getDailyTaskGroupSn()); // dailyTaskGroupSn
		obj.put(K.dailyTasks, getDailyTasks()); // dailyTasks
		obj.put(K.dailyTaskRecv, isDailyTaskRecv()); // dailyTaskRecv
		obj.put(K.unlockedMaxGround, getUnlockedMaxGround()); // unlockedMaxGround
		obj.put(K.fishGroundTasks, getFishGroundTasks()); // fishGroundTasks
		obj.put(K.totalFishCount, getTotalFishCount()); // totalFishCount
		obj.put(K.dailyReelSuccCount, getDailyReelSuccCount()); // dailyReelSuccCount
		obj.put(K.autoFishGroundSn, getAutoFishGroundSn()); // autoFishGroundSn
		obj.put(K.autoFishStartTime, getAutoFishStartTime()); // autoFishStartTime
		obj.put(K.autoFishBaitsSnNum, getAutoFishBaitsSnNum()); // autoFishBaitsSnNum
		obj.put(K.autoFishCastNum, getAutoFishCastNum()); // autoFishCastNum
		obj.put(K.autoFishSettleCount, getAutoFishSettleCount()); // autoFishSettleCount
		obj.put(K.firstFishMap, getFirstFishMap()); // firstFishMap
		obj.put(K.totalAlbumFishes, getTotalAlbumFishes()); // totalAlbumFishes
		obj.put(K.totalSellFishes, getTotalSellFishes()); // totalSellFishes
		obj.put(K.totalCastCnt, getTotalCastCnt()); // totalCastCnt
		obj.put(K.totalReelSuccCnt, getTotalReelSuccCnt()); // totalReelSuccCnt
		obj.put(K.totalSlippedCnt, getTotalSlippedCnt()); // totalSlippedCnt
		obj.put(K.totalRewards, getTotalRewards()); // totalRewards
		obj.put(K.extendJSON, getExtendJSON()); // extendJSON
		return obj;
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(K.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.id, id);
	}
	/**
	 * 钓鱼佬等级
	 */
	public int getFisherLv() {
		return record.get(K.fisherLv);
	}

	public void setFisherLv(final int fisherLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fisherLv, fisherLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fisherLv, fisherLv);
	}
	/**
	 * 钓鱼佬经验
	 */
	public int getFisherExp() {
		return record.get(K.fisherExp);
	}

	public void setFisherExp(final int fisherExp) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fisherExp, fisherExp);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fisherExp, fisherExp);
	}
	/**
	 * 图鉴等级
	 */
	public int getAlbumLv() {
		return record.get(K.albumLv);
	}

	public void setAlbumLv(final int albumLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.albumLv, albumLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.albumLv, albumLv);
	}
	/**
	 * 图鉴经验 (所有鱼类等级之和)
	 */
	public int getAlbumExp() {
		return record.get(K.albumExp);
	}

	public void setAlbumExp(final int albumExp) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.albumExp, albumExp);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.albumExp, albumExp);
	}
	/**
	 * 图鉴总分
	 */
	public int getTotalScore() {
		return record.get(K.totalScore);
	}

	public void setTotalScore(final int totalScore) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalScore, totalScore);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalScore, totalScore);
	}
	/**
	 * 图鉴详情JSONArray K:fishGroupSn, V:fishSn,len,lv,exp的String
	 */
	public String getAlbumDetailMap() {
		return record.get(K.albumDetailMap);
	}

	public void setAlbumDetailMap(final String albumDetailMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.albumDetailMap, albumDetailMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.albumDetailMap, albumDetailMap);
	}
	/**
	 * 渔具类型等级 K:toolType, V:level 的JSON
	 */
	public String getFishTools() {
		return record.get(K.fishTools);
	}

	public void setFishTools(final String fishTools) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fishTools, fishTools);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fishTools, fishTools);
	}
	/**
	 * 鱼塘槽位 K:locationId, V:fishGroupSn 的JSON
	 */
	public String getHouseList() {
		return record.get(K.houseList);
	}

	public void setHouseList(final String houseList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.houseList, houseList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.houseList, houseList);
	}
	/**
	 * 已领取的图鉴评分奖励ID列表
	 */
	public String getAlbumScoreRewards() {
		return record.get(K.albumScoreRewards);
	}

	public void setAlbumScoreRewards(final String albumScoreRewards) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.albumScoreRewards, albumScoreRewards);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.albumScoreRewards, albumScoreRewards);
	}
	/**
	 * 鱼塘装饰
	 */
	public String getHouseDesign() {
		return record.get(K.houseDesign);
	}

	public void setHouseDesign(final String houseDesign) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.houseDesign, houseDesign);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.houseDesign, houseDesign);
	}
	/**
	 * 当前鱼塘装饰
	 */
	public int getCurrentHouseDesign() {
		return record.get(K.currentHouseDesign);
	}

	public void setCurrentHouseDesign(final int currentHouseDesign) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.currentHouseDesign, currentHouseDesign);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.currentHouseDesign, currentHouseDesign);
	}
	/**
	 * 当前每日任务组SN
	 */
	public int getDailyTaskGroupSn() {
		return record.get(K.dailyTaskGroupSn);
	}

	public void setDailyTaskGroupSn(final int dailyTaskGroupSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.dailyTaskGroupSn, dailyTaskGroupSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.dailyTaskGroupSn, dailyTaskGroupSn);
	}
	/**
	 * 每日任务
	 */
	public String getDailyTasks() {
		return record.get(K.dailyTasks);
	}

	public void setDailyTasks(final String dailyTasks) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.dailyTasks, dailyTasks);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.dailyTasks, dailyTasks);
	}
	/**
	 * 是否已领取每日任务最终奖励
	 */
	public boolean isDailyTaskRecv() {
		return record.<Integer>get("dailyTaskRecv") == 1;
	}

	public void setDailyTaskRecv(Object dailyTaskRecv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean dailyTaskRecvbool = false;
		if(dailyTaskRecv instanceof Boolean){
			dailyTaskRecvbool = (boolean)dailyTaskRecv;
		} else if(dailyTaskRecv instanceof Integer){
			dailyTaskRecvbool = (int)dailyTaskRecv == 1;
		} else if(dailyTaskRecv instanceof String){
			if("true".equals(dailyTaskRecv) || "1".equals(dailyTaskRecv)){
				dailyTaskRecvbool = true;
			} else if("false".equals(dailyTaskRecv) || "0".equals(dailyTaskRecv)){
				dailyTaskRecvbool = false;
			}
		}
		record.set(K.dailyTaskRecv, dailyTaskRecvbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.dailyTaskRecv, dailyTaskRecvbool);
	}
	/**
	 * 已解锁的最高常驻渔场ID
	 */
	public int getUnlockedMaxGround() {
		return record.get(K.unlockedMaxGround);
	}

	public void setUnlockedMaxGround(final int unlockedMaxGround) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.unlockedMaxGround, unlockedMaxGround);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.unlockedMaxGround, unlockedMaxGround);
	}
	/**
	 * 渔场解锁任务
	 */
	public String getFishGroundTasks() {
		return record.get(K.fishGroundTasks);
	}

	public void setFishGroundTasks(final String fishGroundTasks) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fishGroundTasks, fishGroundTasks);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fishGroundTasks, fishGroundTasks);
	}
	/**
	 * 总钓鱼次数
	 */
	public int getTotalFishCount() {
		return record.get(K.totalFishCount);
	}

	public void setTotalFishCount(final int totalFishCount) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalFishCount, totalFishCount);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalFishCount, totalFishCount);
	}
	/**
	 * 每日上钩次数
	 */
	public int getDailyReelSuccCount() {
		return record.get(K.dailyReelSuccCount);
	}

	public void setDailyReelSuccCount(final int dailyReelSuccCount) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.dailyReelSuccCount, dailyReelSuccCount);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.dailyReelSuccCount, dailyReelSuccCount);
	}
	/**
	 * 自动钓鱼所在的渔场SN (0代表未开启)
	 */
	public int getAutoFishGroundSn() {
		return record.get(K.autoFishGroundSn);
	}

	public void setAutoFishGroundSn(final int autoFishGroundSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.autoFishGroundSn, autoFishGroundSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.autoFishGroundSn, autoFishGroundSn);
	}
	/**
	 * 自动钓鱼开始时间戳
	 */
	public long getAutoFishStartTime() {
		return record.get(K.autoFishStartTime);
	}

	public void setAutoFishStartTime(final long autoFishStartTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.autoFishStartTime, autoFishStartTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.autoFishStartTime, autoFishStartTime);
	}
	/**
	 * 自动钓鱼选择的鱼饵和数量列表 [[baitSn, num], ...] 的JSON
	 */
	public String getAutoFishBaitsSnNum() {
		return record.get(K.autoFishBaitsSnNum);
	}

	public void setAutoFishBaitsSnNum(final String autoFishBaitsSnNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.autoFishBaitsSnNum, autoFishBaitsSnNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.autoFishBaitsSnNum, autoFishBaitsSnNum);
	}
	/**
	 * 自动钓鱼每次几钓 (1或10)
	 */
	public int getAutoFishCastNum() {
		return record.get(K.autoFishCastNum);
	}

	public void setAutoFishCastNum(final int autoFishCastNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.autoFishCastNum, autoFishCastNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.autoFishCastNum, autoFishCastNum);
	}
	/**
	 * 自动钓鱼已经结算的总次数
	 */
	public int getAutoFishSettleCount() {
		return record.get(K.autoFishSettleCount);
	}

	public void setAutoFishSettleCount(final int autoFishSettleCount) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.autoFishSettleCount, autoFishSettleCount);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.autoFishSettleCount, autoFishSettleCount);
	}
	/**
	 * 地图抛竿吸引到的第一只鱼
	 */
	public String getFirstFishMap() {
		return record.get(K.firstFishMap);
	}

	public void setFirstFishMap(final String firstFishMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.firstFishMap, firstFishMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.firstFishMap, firstFishMap);
	}
	/**
	 * 会话中破纪录的鱼JSONArray K:fishGroupSn, V:fishSn,len,lv,exp的String
	 */
	public String getTotalAlbumFishes() {
		return record.get(K.totalAlbumFishes);
	}

	public void setTotalAlbumFishes(final String totalAlbumFishes) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalAlbumFishes, totalAlbumFishes);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalAlbumFishes, totalAlbumFishes);
	}
	/**
	 * 会话中卖出的鱼 K:fishGroupSn, V:count 的JSON
	 */
	public String getTotalSellFishes() {
		return record.get(K.totalSellFishes);
	}

	public void setTotalSellFishes(final String totalSellFishes) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalSellFishes, totalSellFishes);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalSellFishes, totalSellFishes);
	}
	/**
	 * 会话中总抛竿次数
	 */
	public int getTotalCastCnt() {
		return record.get(K.totalCastCnt);
	}

	public void setTotalCastCnt(final int totalCastCnt) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalCastCnt, totalCastCnt);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalCastCnt, totalCastCnt);
	}
	/**
	 * 会话中总上钩次数
	 */
	public int getTotalReelSuccCnt() {
		return record.get(K.totalReelSuccCnt);
	}

	public void setTotalReelSuccCnt(final int totalReelSuccCnt) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalReelSuccCnt, totalReelSuccCnt);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalReelSuccCnt, totalReelSuccCnt);
	}
	/**
	 * 会话中总逃跑次数
	 */
	public int getTotalSlippedCnt() {
		return record.get(K.totalSlippedCnt);
	}

	public void setTotalSlippedCnt(final int totalSlippedCnt) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalSlippedCnt, totalSlippedCnt);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalSlippedCnt, totalSlippedCnt);
	}
	/**
	 * 会话中获得的总奖励 K:currencyId, V:amount 的JSON
	 */
	public String getTotalRewards() {
		return record.get(K.totalRewards);
	}

	public void setTotalRewards(final String totalRewards) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalRewards, totalRewards);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalRewards, totalRewards);
	}
	/**
	 * 扩展属性(用于处理bug)
	 */
	public String getExtendJSON() {
		return record.get(K.extendJSON);
	}

	public void setExtendJSON(final String extendJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.extendJSON, extendJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.extendJSON, extendJSON);
	}
	 
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}
	
	public void setNew(){
		record.setStatus(DBConsts.RECORD_STATUS_NEW);
	}

		public static String getRedisKeyStr(Object... obj){
		return "HomeFish." + obj[0];
	}

}